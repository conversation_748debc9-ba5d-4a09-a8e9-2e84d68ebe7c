name: nexqloud
description: "NexQloud's Network Monitoring Platform"
publish_to: 'none'
version: 0.1.5

environment:
  sdk: ">=3.6.0 <4.0.0"

dependencies:
  analyzer: ^8.1.1
  flutter:
    sdk: flutter
  glassmorphism: ^3.0.0
  go_router: ^16.2.0
  path: ^1.9.1
  provider: ^6.1.5+1
  flutter_svg: ^2.2.0
  gradient_borders: ^1.0.1
  flutter_glow: ^0.3.2
#  flutter_inset_box_shadow: ^1.0.8
  syncfusion_flutter_maps: ^28.2.4
  syncfusion_flutter_gauges:
    git:
      url: https://github.com/fahadnexqloud/syncfusion_flutter_gauges.git
  fl_chart: ^1.0.0
  syncfusion_flutter_core: ^28.2.4
  url_launcher: ^6.3.2
  animate_do: ^4.2.0
  syncfusion_flutter_datagrid: ^28.2.4
  excel: ^4.0.6
  path_provider: ^2.1.5
  lottie: ^3.1.3
  equatable: ^2.0.7
  wolt_modal_sheet: ^0.11.0
  flutter_inset_shadow: ^2.0.3
  flutter_neumorphic_plus: ^3.5.0
  sentry_flutter: ^9.6.0
  device_info_plus: ^11.5.0
  http: ^1.5.0
  cached_network_image: ^3.4.1



dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

  yaml: any
flutter:
  uses-material-design: true

  assets:
    - assets/.
    - assets/world_map.json
    - assets/world_map.json
    - assets/servers_list.xlsx
    - assets/icons/svg/.
    - assets/icons/png/.
    - assets/images/png/.
    - assets/fonts/rubik/.
    - assets/icons/svg/nex_logo.svg

  fonts:
    - family: Rubik
      fonts:
        - asset: assets/fonts/rubik/Rubik-Bold.ttf
        - asset: assets/fonts/rubik/Rubik-ExtraBold.ttf
        - asset: assets/fonts/rubik/Rubik-Light.ttf
        - asset: assets/fonts/rubik/Rubik-Medium.ttf
        - asset: assets/fonts/rubik/Rubik-Regular.ttf
        - asset: assets/fonts/rubik/Rubik-SemiBold.ttf