version: 0.2
phases:
  install: # Install kubectl and helm
    commands:
      - mkdir -p /tmpinstalldir
      - cd /tmpinstalldir
      - curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
      - curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
      - sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
      - cd $CODEBUILD_SRC_DIR
  pre_build:
    commands:
      - case "${BRANCH}" in "main") aws eks update-kubeconfig --name nexqloud; export API_URL="https://network-map-backend.nexqloud.net";; "stage") mkdir -p ~/.kube; aws ssm get-parameters --names dcp-kubeconfig --with-decryption | jq .Parameters[0].Value -r > ~/.kube/config; export API_URL="https://stagenetwork-map-backend.dks.nexqloud.net";; "dev") mkdir -p ~/.kube; aws ssm get-parameters --names dcp-kubeconfig --with-decryption | jq .Parameters[0].Value -r > ~/.kube/config; export API_URL="https://devnetwork-map-backend.dks.nexqloud.net";; *) mkdir -p ~/.kube; aws ssm get-parameters --names dcp-kubeconfig --with-decryption | jq .Parameters[0].Value -r > ~/.kube/config; export API_URL="https://devnetwork-map-backend.dks.nexqloud.net";; esac
  build: # Build Docker image and tag it with the commit sha
    commands:
      - echo ${BRANCH}
      - docker build -t 815483408161.dkr.ecr.us-east-1.amazonaws.com/${REPO_NAME}:${BRANCH}.${CODEBUILD_BUILD_NUMBER} --no-cache --pull --build-arg API_URL=$API_URL .
  post_build: # Push the Docker image to the ECR
    commands:
      - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 815483408161.dkr.ecr.us-east-1.amazonaws.com
      - docker push 815483408161.dkr.ecr.us-east-1.amazonaws.com/${REPO_NAME}:${BRANCH}.${CODEBUILD_BUILD_NUMBER}
      - mkdir -p /tmpdeploydir
      - cd /tmpdeploydir
      - kubectl -n default get secret helm-values-deploy-key  -o json | jq -r   .data.privkey | base64 -d > private_key_file
      - chmod 600 private_key_file
      - GIT_SSH_COMMAND='ssh -i private_key_file' <NAME_EMAIL>:nexqloud/helm-values.git
      - cd helm-values/${REPO_NAME}
      - RN=$(echo ${REPO_NAME} | tr _ -)
      - if [ "${BRANCH}" = "main" ]; then PREFIX=""; else PREFIX="${BRANCH}-"; fi
      - helm upgrade --install -n ${PREFIX}${RN} ${RN}  oci://815483408161.dkr.ecr.us-east-1.amazonaws.com/nexqloud -f values.yaml  -f ${BRANCH}.yaml  --set-literal image.tag=${BRANCH}.${CODEBUILD_BUILD_NUMBER}
