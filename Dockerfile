# Use the Dart official base image
# Use Dart 3.5.4 as the base image
FROM dart:3.5.4 AS build

# Set the working directory
WORKDIR /app

# Install dependencies for Flutter
RUN apt-get update && apt-get install -y \
  git \
  curl \
  unzip \
  xz-utils \
  zip \
  && rm -rf /var/lib/apt/lists/*

# Install Flutter (latest stable version compatible with Dart 3.5.4)
RUN git clone https://github.com/flutter/flutter.git /flutter
WORKDIR /flutter
RUN git fetch --all && git checkout stable

# Add Flutter to PATH
ENV PATH="/flutter/bin:${PATH}"

# Set the working directory to your app
WORKDIR /app

# Ensure Flutter is set up with the correct environment
RUN flutter doctor

COPY pubspec.* ./
# Copy your application code
COPY . .

# Run Flutter pub get to install dependencies
RUN flutter pub get

ARG API_URL
RUN VERSION=$(grep '^version:' pubspec.yaml | awk '{print $2}') && \
    echo "Building version $VERSION" && \
    flutter build web --release -t lib/main.dart --source-maps --no-tree-shake-icons --dart-define=API_URL=${API_URL} --dart-define=APP_VERSION=$VERSION

RUN cd build/web && \
    HASH=$(sha256sum main.dart.js | awk '{print $1}') && \
    mv main.dart.js main.dart.${HASH}.js && \
    sed -i "s/main.dart.js/main.dart.${HASH}.js/g" index.html flutter.js flutter_bootstrap.js flutter_service_worker.js && \
    mv flutter_bootstrap.js flutter_bootstrap.${HASH}.js && \
    sed -i "s/flutter_bootstrap.js/flutter_bootstrap.${HASH}.js/g" index.html flutter.js flutter_service_worker.js

# Use the official Nginx image to serve the Flutter web app
FROM nginx:latest

# Switch back to root for configuring Nginx
USER root

# Copy the build output from the Flutter stage to the Nginx HTML directory
COPY --from=build /app/build/web /usr/share/nginx/html

# Copy custom Nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose HTTP port
EXPOSE 9000

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
