const regionMap = <String, String>{
  'us-east-1': 'Virginia',
  'us-east-2': 'Ohio',
  'eu-north-1': 'Stockholm',
  'eu-south-1': 'Milan',
  'us-west-1': 'California',
  'us-west-2': 'Oregon',
  'eu-west-1': 'Ireland',
  'eu-west-2': 'London',
  'eu-west-3': 'Paris',
  'eu-central-1': 'Frankfurt',
  'sa-east-1': 'Sao Paulo',
  'ap-southeast-1': 'Singapore',
  'ap-southeast-2': 'Sydney',
  'ap-northeast-1': 'Tokyo',
  'ap-northeast-2': 'Seoul',
  'ap-northeast-3': 'Osaka',
  'ap-south-1': 'Mumbai',
  'ca-central-1': 'Canada Central',
  'ap-east-1': 'Hong Kong',
  'me-south-1': 'Bahrain',
  'af-south-1': 'South Africa',
};

final regionsWithLatLng = <Map<String, dynamic>>[
  {'region': 'us-east-1', 'name': 'Virginia', 'lat': 38.13, 'lng': -78.45},
  {'region': 'us-east-2', 'name': 'Ohio', 'lat': 39.96, 'lng': -83},
  {'region': 'eu-north-1', 'name': 'Stockholm', 'lat': 59.25, 'lng': 17.81},
  {'region': 'eu-south-1', 'name': 'Milan', 'lat': 45.43, 'lng': 9.29},
  {'region': 'us-west-1', 'name': 'California', 'lat': 37.35, 'lng': -121.96},
  {'region': 'us-west-2', 'name': 'Oregon', 'lat': 46.15, 'lng': -123.88},
  {'region': 'eu-west-1', 'name': 'Ireland', 'lat': 53.0, 'lng': -8},
  {'region': 'eu-west-2', 'name': 'London', 'lat': 51.0, 'lng': -0.1},
  {'region': 'eu-west-3', 'name': 'Paris', 'lat': 48.86, 'lng': 2.35},
  {'region': 'eu-central-1', 'name': 'Frankfurt', 'lat': 50.0, 'lng': 8},
  {'region': 'sa-east-1', 'name': 'Sao Paulo', 'lat': -23.34, 'lng': -46.38},
  {'region': 'ap-southeast-1', 'name': 'Singapore', 'lat': 1.37, 'lng': 103.8},
  {'region': 'ap-southeast-2', 'name': 'Sydney', 'lat': -33.86, 'lng': 151.2},
  {'region': 'ap-northeast-1', 'name': 'Tokyo', 'lat': 35.41, 'lng': 139.42},
  {'region': 'ap-northeast-2', 'name': 'Seoul', 'lat': 37.56, 'lng': 126.98},
  {'region': 'ap-northeast-3', 'name': 'Osaka', 'lat': 34.69, 'lng': 135.49},
  {'region': 'ap-south-1', 'name': 'Mumbai', 'lat': 19.08, 'lng': 72.88},
  {
    'region': 'ca-central-1',
    'name': 'Canada Central',
    'lat': 45.5,
    'lng': -73.6
  },
  {'region': 'ap-east-1', 'name': 'Hong Kong', 'lat': 22.27, 'lng': 114.16},
  {'region': 'me-south-1', 'name': 'Bahrain', 'lat': 26.10, 'lng': 50.46},
  {'region': 'af-south-1', 'name': 'South Africa', 'lat': -33.93, 'lng': 18.42},
];

String getRegionName(String region, {bool isShort = false}) {
  // Define a map of region code to human-readable region name.
  const regionMap = <String, String>{
    'us-east-1': 'Virginia',
    'us-east-2': 'Ohio',
    'eu-north-1': 'Stockholm',
    'eu-south-1': 'Milan',
    'us-west-1': 'California',
    'us-west-2': 'Oregon',
    'eu-west-1': 'Ireland',
    'eu-west-2': 'London',
    'eu-west-3': 'Paris',
    'eu-central-1': 'Frankfurt',
    'sa-east-1': 'Sao Paulo',
    'ap-southeast-1': 'Singapore',
    'ap-southeast-2': 'Sydney',
    'ap-northeast-1': 'Tokyo',
    'ap-northeast-2': 'Seoul',
    'ap-northeast-3': 'Osaka',
    'ap-south-1': 'Mumbai',
    'ca-central-1': 'Canada Central',
    'ap-east-1': 'Hong Kong',
    'me-south-1': 'Bahrain',
    'af-south-1': 'South Africa',
  };

  // Return the mapped name along with the region code in parentheses.
  if (regionMap.containsKey(region)) {
    return '${regionMap[region]}${isShort ? '' : ' ($region)'}';
  } else {
    return 'Unknown';
  }
}

String getRegionCodeName(String region, {bool isShort = false}) {
  // Define a map of region code to human-readable region name.
  const regionMap = <String, String>{
    'us-east-1': 'Virginia',
    'us-east-2': 'Ohio',
    'eu-north-1': 'Stockholm',
    'eu-south-1': 'Milan',
    'us-west-1': 'California',
    'us-west-2': 'Oregon',
    'eu-west-1': 'Ireland',
    'eu-west-2': 'London',
    'eu-west-3': 'Paris',
    'eu-central-1': 'Frankfurt',
    'sa-east-1': 'Sao Paulo',
    'ap-southeast-1': 'Singapore',
    'ap-southeast-2': 'Sydney',
    'ap-northeast-1': 'Tokyo',
    'ap-northeast-2': 'Seoul',
    'ap-northeast-3': 'Osaka',
    'ap-south-1': 'Mumbai',
    'ca-central-1': 'Canada Central',
    'ap-east-1': 'Hong Kong',
    'me-south-1': 'Bahrain',
    'af-south-1': 'South Africa',
  };

  // Return the mapped name along with the region code in parentheses.
  if (regionMap.containsKey(region)) {
    return region;
  } else {
    return 'Unknown';
  }
}

double adjustBytes(double bytes) {
  final mb = bytes / 1_000_000;

  // Check if MB is in the range 980 to 1000
  if (mb >= 980 && mb < 1000) {
    bytes += 20 * 1_000_000;
  }

  return bytes;
}

double getDefaultRegionLat(String region) {
  // Search for the region in the list.
  final entry = regionsWithLatLng.firstWhere(
    (map) => map['region'] == region,
    orElse: () => <String, dynamic>{},
  );
  // Return the latitude, or default to 0.0 if not found.
  return entry['lat'] ?? 0.0;
}

double getDefaultRegionLng(String region) {
  // Search for the region in the list.
  final entry = regionsWithLatLng.firstWhere(
    (map) => map['region'] == region,
    orElse: () => <String, dynamic>{},
  );
  // Return the longitude, or default to 0.0 if not found.
  return entry['lng'] ?? 0.0;
}
