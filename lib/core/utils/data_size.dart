class DataSize {
  DataSize({
    required this.kb,
    required this.gb,
    required this.tb,
  });
  final double kb;
  final double gb;
  final double tb;

  @override
  String toString() {
    return '${kb.toStringAsFixed(2)} KB, ${gb.toStringAsFixed(6)} GB, ${tb.toStringAsFixed(9)} TB';
  }
}

DataSize convertBytes(double bytes) {
  final kb = bytes / 1000;
  final gb = kb / 1000;
  final tb = gb / 1000;

  return DataSize(kb: kb, gb: gb, tb: tb);
}

String dataSizeUnit(double bytes, {bool showSmallUnits = false}) {
  try {
    if (bytes >= 1_000_000_000_000) {
      return showSmallUnits ? 'TB/s' : 'TBit/s';
    } else if (bytes >= 1_000_000_000) {
      return showSmallUnits ? 'GB/s' : 'GBit/s';
    } else if (bytes >= 1_000_000) {
      return showSmallUnits ? 'MB/s' : 'MBit/s';
    } else if (bytes >= 1_000) {
      return showSmallUnits ? 'KB/s' : 'KBit/s';
    } else {
      return 'B';
    }
  } catch (e) {
    return 'B';
  }
}

String formatBytes(
  double bytes, {
  bool showUnits = true,
}) {
  try {
    if (showUnits) {
      if (bytes >= 1_000_000_000_000) {
        final tb = bytes / 1_000_000_000_000;
        return '${tb.toStringAsFixed(2)}TB';
      } else if (bytes >= 1_000_000_000) {
        final gb = bytes / 1_000_000_000;
        return '${gb.toStringAsFixed(2)}GB';
      } else if (bytes >= 1_000_000) {
        final mb = bytes / 1_000_000;
        return '${mb.toStringAsFixed(2)}MB';
      } else if (bytes >= 1_000) {
        final kb = bytes / 1_000;
        return '${kb.toStringAsFixed(2)}KB';
      } else {
        return '${bytes.toStringAsFixed(2)}B';
      }
    } else {
      if (bytes >= 1_000_000_000_000) {
        final tb = bytes / 1_000_000_000_000;
        return tb.toStringAsFixed(2);
      } else if (bytes >= 1_000_000_000) {
        final gb = bytes / 1_000_000_000;
        return gb.toStringAsFixed(2);
      } else if (bytes >= 1_000_000) {
        final mb = bytes / 1_000_000;
        return mb.toStringAsFixed(2);
      } else if (bytes >= 1_000) {
        final kb = bytes / 1_000;
        return kb.toStringAsFixed(2);
      } else {
        return bytes.toStringAsFixed(2);
      }
    }
  } catch (e) {
    // Error in data size calculation: $e
    return bytes.toStringAsFixed(2);
  }
}
