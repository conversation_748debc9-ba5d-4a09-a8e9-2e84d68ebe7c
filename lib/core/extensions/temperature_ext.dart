extension TemperatureExtension on num {
  /// Converts Celsius temperature to Fahrenheit
  /// Formula: °F = (°C × 9/5) + 32
  double get toFahrenheit => (this * 9 / 5) + 32;

  /// Converts Celsius temperature to Fahrenheit with specified decimal places
  /// Formula: °F = (°C × 9/5) + 32
  double toFahrenheitAsDouble([int decimalPlaces = 1]) {
    return double.parse(toFahrenheit.toStringAsFixed(decimalPlaces));
  }

  /// Converts Celsius temperature to Fahrenheit as a formatted string
  /// Formula: °F = (°C × 9/5) + 32
  String toFahrenheitAsString([int decimalPlaces = 1]) {
    return toFahrenheit.toStringAsFixed(decimalPlaces);
  }

  /// Converts Celsius temperature to Fahrenheit with °F symbol
  /// Formula: °F = (°C × 9/5) + 32
  String get toFahrenheitWithSymbol {
    return '${toFahrenheit.toStringAsFixed(1)} °F';
  }

  /// Converts Celsius temperature to Fahrenheit with °F symbol and custom decimal places
  /// Formula: °F = (°C × 9/5) + 32
  String toFahrenheitWithSymbolCustom([int decimalPlaces = 1]) {
    return '${toFahrenheit.toStringAsFixed(decimalPlaces)} °F';
  }
}
