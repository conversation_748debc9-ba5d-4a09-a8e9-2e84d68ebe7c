import 'package:flutter/material.dart';

extension ResponsiveFont on BuildContext {
  double responsiveFontSize(double baseFontSize) {
    final screenWidth = MediaQuery.sizeOf(this).width;
    // Adjust the base font size based on screen width
    if (screenWidth < 360) {
      return baseFontSize * 0.7;
    } else if (screenWidth < 480) {
      return baseFontSize * 0.9;
    } else if (screenWidth < 600) {
      return baseFontSize;
    } else if (screenWidth < 720) {
      return baseFontSize * 1.1;
    } else {
      return baseFontSize * 1.2;
    }
  }
}

class ResponsiveFontService {
  factory ResponsiveFontService() {
    return _instance;
  }

  ResponsiveFontService._internal();
  static const double _baseWidth = 1920; // Base design width
  static const double _baseHeight = 1080; // Base design height

  // Base font sizes for different text styles
  static const double _baseHeading1Size = 32;
  static const double _baseHeading2Size = 28;
  static const double _baseHeading3Size = 24;
  static const double _baseBodySize = 16;
  static const double _baseSmallSize = 14;

  // Minimum and maximum font size constraints
  static const double _minFontSize = 12;
  static const double _maxFontSize = 40;

  // Singleton instance
  static final ResponsiveFontService _instance =
      ResponsiveFontService._internal();

  // Calculate scale factor based on screen width
  double _getScaleFactor(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    final height = size.height;

    // Use the smaller scaling factor to ensure text remains readable
    final widthScale = width / _baseWidth;
    final heightScale = height / _baseHeight;

    return widthScale < heightScale ? widthScale : heightScale;
  }

  // Get responsive font size with constraints
  double _getResponsiveSize(BuildContext context, double baseSize) {
    final scaleFactor = _getScaleFactor(context);
    final calculatedSize = baseSize * scaleFactor;

    return calculatedSize.clamp(_minFontSize, _maxFontSize);
  }

  // Public methods to get different text styles
  TextStyle getHeading1Style(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(context, _baseHeading1Size),
      fontWeight: FontWeight.bold,
      height: 1.2,
    );
  }

  TextStyle getHeading2Style(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(context, _baseHeading2Size),
      fontWeight: FontWeight.bold,
      height: 1.2,
    );
  }

  TextStyle getHeading3Style(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(context, _baseHeading3Size),
      fontWeight: FontWeight.w600,
      height: 1.2,
    );
  }

  TextStyle getBodyStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(context, _baseBodySize),
      height: 1.5,
    );
  }

  TextStyle getSmallStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getResponsiveSize(context, _baseSmallSize),
      height: 1.5,
    );
  }

  // Custom size method for special cases
  TextStyle getCustomStyle(
    BuildContext context, {
    required double baseSize,
    FontWeight? fontWeight,
    double? height,
  }) {
    return TextStyle(
      fontSize: _getResponsiveSize(context, baseSize),
      fontWeight: fontWeight,
      height: height,
    );
  }
}

// Extension method for easier access
extension ResponsiveTextContext on BuildContext {
  ResponsiveFontService get textService => ResponsiveFontService();
}
