extension PercentageExtension on num {
  String get toPercentage => (this * 100).toStringAsFixed(2);

  // convert to TB if GB is greater than or equal to 1000 GB
  double get toTB => this >= 1000 ? (this / 1024) : toDouble();

  bool get isTB => this >= 1000;

  // I want to convert the values
  // in this format 8.64K I will give only number
  String get toK {
    if (this >= 1000) {
      return (this / 1000).toStringAsFixed(2) + 'K';
    }
    return this.toString();
  }
}
