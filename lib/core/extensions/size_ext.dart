import 'dart:ui';

import 'package:flutter/material.dart';

extension SizeExt on BuildContext {
  FlutterView get view =>
      WidgetsBinding.instance.platformDispatcher.views.first;

// Dimensions in physical pixels (px)
  Size get vSize => view.physicalSize;
  double get vWidth => vSize.width;
  double get vHeight =>
      MediaQuery.of(this).size.height +
      MediaQuery.of(this).padding.top +
      MediaQuery.of(this).padding.bottom;

  double get height => MediaQuery.sizeOf(this).height;
  double get width => MediaQuery.sizeOf(this).width;
  Orientation get orientation => MediaQuery.of(this).orientation;

  bool get isLandscape => orientation == Orientation.landscape;

  double get sizeRatio => width + height;

  double get horizontalPadding => isMobileOrientationWide
      ? width * 0.75
      : isMobile
          ? width * 0.9
          : sizeRatio *
              ((width < 1300 && !isTablet)
                  ? 0.45
                  : isDesktop
                      ? 0.427
                      : 0.4);

  double get horizontalPadding0 => isDesktopLarge
      ? width * 0.63
      : isDesktop
          ? width * 0.63
          : width * 0.63;

  double get horizontalPadding2 {
    // Ultra-wide or very large screens
    if (width >= 2250) return width * 0.75;

    // Large desktop screens
    if (width >= 1920) return width * 0.63;

    // Standard desktop screens
    if (width >= 1040) return width * 0.63;

    // Tablet screens
    if (width >= 768) return width * 0.55;

    // Mobile screens
    return width * 0.65;
  }

  String get screenSizeCategory {
    if (width < 768) return 'Mobile';
    if (width < 1280) return 'Tablet';
    if (width < 1920) return 'Desktop';
    if (width < 2560) return 'Large Desktop';
    return 'Ultra-wide';
  }

  ///Platform Checks
  bool get isMobile => width < 768;
  bool get isTablet => width >= 768 && width < 1024;
  bool get isTabletLarge =>
      width <= 2235 && width > 1280 && height <= 2914.5 && height > 1280;
  bool get isDesktopExtraSmall => sizeRatio < 2090;
  bool get isDesktopSmall => sizeRatio < 2350 && sizeRatio >= 2090;
  bool get isDesktop => width >= 1024;
  bool get isDesktopLarge => width >= 2250;
  bool get isMobileOrientationWide => width <= 1024 && height <= 600;
  bool get isDesktopDevices => isDesktop || isDesktopLarge || isDesktopSmall;
}

/// Screen Sizes testing
// 1024x768
// 1280x720
// 1280x800
// 1280x960
// 1280x1024
// 1366x768
// 1440x900
// 1600x1200
// 1680x1050
// 1920x1080
// 1920x1200
// 2048x1536
// 2560x1440
