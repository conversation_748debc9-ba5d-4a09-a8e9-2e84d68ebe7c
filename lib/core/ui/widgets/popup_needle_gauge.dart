import 'dart:math';

import 'package:flutter/material.dart';
import 'package:nexqloud/core/constants/colors.dart';

class PopupNeedleGauge extends StatefulWidget {
  const PopupNeedleGauge({
    super.key,
    required this.progress,
    this.ticksStartFrom = 16.0, // Adjust based on gauge size
    this.centerWidget,
    this.size, // Optional size for explicit control
    required this.strokeWidth, // Required stroke width for arc thickness
    this.ticksCount = 30, // Number of ticks to display
    this.showTicks = true,
    this.radiusFactor = 1.0, // The factor to control size of all elements
  });

  final double progress; // Value between 0 and 100
  final Widget? centerWidget; // Optional widget below the gauge
  final double? size; // Optional size (diameter or width)
  final double strokeWidth; // Thickness of the arcs, required
  final double ticksStartFrom; // Adjust based on gauge size
  final int ticksCount; // Number of ticks to display
  final bool showTicks;
  final double radiusFactor; // Factor to control size of the gauge

  @override
  PopupNeedleGaugeState createState() => PopupNeedleGaugeState();
}

class PopupNeedleGaugeState extends State<PopupNeedleGauge>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    final progress =
        widget.progress.clamp(0.0, 100.0); // Clamp progress between 0 and 100
    _animation = Tween<double>(begin: 0, end: progress).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    )..addListener(() {
        setState(() {});
      });

    _controller.forward();
  }

  @override
  void didUpdateWidget(PopupNeedleGauge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      final progress = widget.progress.clamp(0.0, 100.0);
      _animation =
          Tween<double>(begin: _animation.value, end: progress).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Curves.easeInOut,
        ),
      );
      _controller
        ..reset()
        ..forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use provided size or smallest constraint dimension
        final gaugeSize =
            widget.size ?? min(constraints.maxWidth, constraints.maxHeight);
        final gaugeHeight = gaugeSize * 0.8; // Adjust ratio as needed

        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: gaugeSize,
              height: gaugeHeight,
              child: CustomPaint(
                painter: _NeedleGaugePainter(
                  animatedProgress: _animation.value,
                  strokeWidth: widget.strokeWidth,
                  ticksStartFrom: widget.ticksStartFrom,
                  ticksLength: widget.ticksCount,
                  showTicks: widget.showTicks,
                  radiusFactor: widget.radiusFactor,
                ),
              ),
            ),
            const SizedBox(height: 10),
            widget.centerWidget ?? const SizedBox(),
            const SizedBox(height: 4),
          ],
        );
      },
    );
  }
}

class _NeedleGaugePainter extends CustomPainter {
  _NeedleGaugePainter({
    required this.animatedProgress,
    required this.strokeWidth,
    this.ticksStartFrom = 16.0,
    this.ticksLength = 30,
    this.showTicks = true,
    this.radiusFactor = 1.0, // Control size factor
  });

  final double animatedProgress;
  final double strokeWidth;
  final double ticksStartFrom;
  final int ticksLength;
  final bool showTicks;
  final double radiusFactor; // Control size factor

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 1.3);
    final radius = (min(size.width / 2, size.height / 1.2) - strokeWidth / 2) *
        radiusFactor;
    final rect = Rect.fromCircle(center: center, radius: radius);

    const startAngle = pi * 3 / 4;
    const sweepAngle = pi * 3 / 2;

    // Background arc
    final backgroundPaint = Paint()
      ..color = Colors.white.withOpacity(0.95)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    // Left border (shadow effect)
    final leftBorderPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth * 0.9
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6)
      ..strokeCap = StrokeCap.round;

    // Progress arc
    final progressPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    const gradient = LinearGradient(
      colors: [Color(0xFF33B1FF), Color(0xFF9933FF)],
    );
    progressPaint.shader = gradient.createShader(rect);

    // Draw background arc and borders
    canvas.drawArc(rect, startAngle, sweepAngle, false, backgroundPaint);

    // // Left border: Slightly offset to emphasize the start (left side)
    // const leftBorderAngleOffset = -0.05; // Small angular offset for left
    // canvas.drawArc(
    //   rect,
    //   startAngle,
    //   sweepAngle + leftBorderAngleOffset,
    //   false,
    //   leftBorderPaint,
    // );

    // Progress arc and glow
    final progressSweepAngle = (animatedProgress / 100) * sweepAngle;
    canvas.drawArc(rect, startAngle, progressSweepAngle, false, progressPaint);

    final glowPaint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth + 12
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10)
      ..color = const Color(0x80A1C6FF);

    canvas.drawArc(rect, startAngle, progressSweepAngle, false, glowPaint);

    // Shadow for background
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10);
    canvas.drawArc(rect, startAngle, sweepAngle, false, shadowPaint);

    // Top White (shadow effect)
    final toNegativeEffectPaint = Paint()
      ..color = Colors.white.withOpacity(0.13)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth * 0.9
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6)
      ..strokeCap = StrokeCap.round;
    final gradientShadow = LinearGradient(
      colors: [
        kWhite,
        kWhite,
        kWhite.withOpacity(0.3),
        kWhite.withOpacity(0.5),
        kWhite,
      ],
    );
    leftBorderPaint.shader = gradientShadow.createShader(rect);

    canvas.drawArc(
      rect,
      startAngle,
      progressSweepAngle,
      false,
      toNegativeEffectPaint,
    );

    // Ticks (unchanged)
    if (showTicks) {
      final tickStartOffset = ticksStartFrom;
      const tickEndOffset = 12.0;
      for (var i = 0; i <= ticksLength; i++) {
        final angle = startAngle + (i / ticksLength) * sweepAngle;
        final tickLength = (i % 5 == 0) ? tickEndOffset : tickEndOffset / 1.5;
        final startX = center.dx + (radius - tickStartOffset) * cos(angle);
        final startY = center.dy + (radius - tickStartOffset) * sin(angle);
        final endX =
            center.dx + (radius - tickStartOffset - tickLength) * cos(angle);
        final endY =
            center.dy + (radius - tickStartOffset - tickLength) * sin(angle);
        final tickPaint = Paint()
          ..color = Colors.white.withOpacity(0.8)
          ..strokeWidth = (i % 5 == 0) ? 2.0 : 1.5
          ..strokeCap = StrokeCap.round;
        canvas.drawLine(Offset(startX, startY), Offset(endX, endY), tickPaint);
      }
    }

    // Needle (unchanged)
    final needleAngle = startAngle + (animatedProgress / 100) * sweepAngle;
    final needleLength = radius - strokeWidth / 2 - 10;
    const needleWidth = 14.0 / 2;

    final tipX = center.dx + needleLength * cos(needleAngle);
    final tipY = center.dy + needleLength * sin(needleAngle);

    final baseLeftX = center.dx + (needleWidth / 2) * cos(needleAngle + pi / 2);
    final baseLeftY = center.dy + (needleWidth / 2) * sin(needleAngle + pi / 2);

    final baseRightX =
        center.dx + (needleWidth / 2) * cos(needleAngle - pi / 2);
    final baseRightY =
        center.dy + (needleWidth / 2) * sin(needleAngle - pi / 2);

    final needlePath = Path()
      ..moveTo(tipX, tipY)
      ..lineTo(baseLeftX, baseLeftY)
      ..arcToPoint(
        Offset(baseRightX, baseRightY),
        radius: const Radius.circular(needleWidth / 2),
        clockwise: false,
      )
      ..close();

    final needlePaint = Paint()
      ..color = const Color(0xFF8052FF)
      ..style = PaintingStyle.fill;

    canvas.drawPath(needlePath, needlePaint);

    // Center circle (unchanged)
    final centerCirclePaint = Paint()..color = Colors.white;
    const centerCircleRadius = 3.0;
    canvas.drawCircle(center, centerCircleRadius, centerCirclePaint);
  }

  @override
  bool shouldRepaint(covariant _NeedleGaugePainter oldDelegate) {
    return animatedProgress != oldDelegate.animatedProgress ||
        strokeWidth != oldDelegate.strokeWidth;
  }
}
