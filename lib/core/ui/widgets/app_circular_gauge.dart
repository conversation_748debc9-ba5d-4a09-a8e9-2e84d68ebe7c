import 'dart:math';

import 'package:flutter/material.dart';
import 'package:nexqloud/core/constants/colors.dart';

class AppCircularGauge extends StatefulWidget {
  const AppCircularGauge({
    super.key,
    required this.progress,
    required this.strokeWidth,
    this.centerWidget,
    this.size, // Optional size for the gauge (diameter)
  });

  final double progress; // Value between 0 and 100
  final Widget? centerWidget; // Optional widget to display in the center
  final double? size; // Optional size for the gauge (diameter)
  final double strokeWidth; // Thickness of the arcs

  @override
  AppCircularGaugeState createState() => AppCircularGaugeState();
}

class AppCircularGaugeState extends State<AppCircularGauge>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0, end: widget.progress).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    )..addListener(() {
        setState(() {});
      });

    _controller.forward();
  }

  @override
  void didUpdateWidget(AppCircularGauge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _animation =
          Tween<double>(begin: _animation.value, end: widget.progress).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Curves.easeInOut,
        ),
      );
      _controller
        ..reset()
        ..forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Determine the size: use provided size or smallest constraint dimension
          final gaugeSize =
              widget.size ?? min(constraints.maxWidth, constraints.maxHeight);

          return SizedBox(
            width: gaugeSize,
            height: gaugeSize,
            child: Stack(
              alignment: Alignment.center,
              children: [
                CustomPaint(
                  size: Size(gaugeSize, gaugeSize),
                  painter: _RoundGaugePainter(
                    animatedProgress: _animation.value,
                    strokeWidth:
                        widget.strokeWidth, // Pass strokeWidth to painter
                  ),
                ),
                if (widget.centerWidget != null) widget.centerWidget!,
              ],
            ),
          );
        },
      ),
    );
  }
}

class _RoundGaugePainter extends CustomPainter {
  _RoundGaugePainter({
    required this.animatedProgress,
    required this.strokeWidth, // Receive strokeWidth from widget
  });

  final double animatedProgress;
  final double strokeWidth;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width / 2, size.height / 2) - strokeWidth / 2;
    final rect = Rect.fromCircle(center: center, radius: radius);

    const startAngle = -pi / 2;
    const sweepAngle = pi * 2;

    final backgroundPaint = Paint()
      ..color = Colors.white.withOpacity(0.95)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth // Use adjustable strokeWidth
      ..strokeCap = StrokeCap.round;

    final progressPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth // Use adjustable strokeWidth
      ..strokeCap = StrokeCap.round;

    const gradient = LinearGradient(
      colors: [gradientColorOne, gradientColorThree],
    );
    progressPaint.shader = gradient.createShader(rect);

    canvas.drawArc(rect, startAngle, sweepAngle, false, backgroundPaint);

    final progressSweepAngle = (animatedProgress / 100) * sweepAngle;
    canvas.drawArc(rect, startAngle, progressSweepAngle, false, progressPaint);

    final glowPaint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth + 12 // Adjust glow based on strokeWidth
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10)
      ..color = const Color(0x80A1C6FF);

    canvas.drawArc(rect, startAngle, progressSweepAngle, false, glowPaint);

    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.085)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth // Use adjustable strokeWidth
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10);
    canvas.drawArc(rect, startAngle, sweepAngle, false, shadowPaint);

    // Top White (shadow effect)
    final toNegativeEffectPaint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth * 0.9
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6)
      ..strokeCap = StrokeCap.round;
    final gradientShadow = LinearGradient(
      colors: [
        kWhite,
        kWhite,
        kWhite.withOpacity(0.3),
        kWhite.withOpacity(0.5),
        kWhite,
      ],
    );
    toNegativeEffectPaint.shader = gradientShadow.createShader(rect);

    canvas.drawArc(
      rect,
      startAngle,
      progressSweepAngle,
      false,
      toNegativeEffectPaint,
    );
  }

  @override
  bool shouldRepaint(covariant _RoundGaugePainter oldDelegate) {
    return animatedProgress != oldDelegate.animatedProgress ||
        strokeWidth != oldDelegate.strokeWidth; // Optimize repainting
  }
}
