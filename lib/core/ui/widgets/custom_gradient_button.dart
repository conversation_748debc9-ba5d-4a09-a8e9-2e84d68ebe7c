import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_glow/flutter_glow.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/utils/box_shadow/flutter_inset_box_decoration.dart';
import 'package:nexqloud/core/utils/box_shadow/flutter_inset_box_shadow.dart';

class CustomGradientButton extends StatelessWidget {
  const CustomGradientButton({
    super.key,
    required this.title,
    required this.onTap,
    this.trailing,
    this.isEnable = true,
    this.addShadow = true,
    this.isText20 = false,
    this.isFromWallet = false,
    this.preFix,
    this.hideGlow = false,
    this.hideContainerColor = false,
    this.radius = 100,
    this.containerHeight = 48,
    this.fontSize = 11,
  });
  final String title;
  final VoidCallback? onTap;
  final Widget? trailing;
  final Widget? preFix;
  final bool isEnable;
  final bool addShadow;
  final bool isText20;
  final bool isFromWallet;
  final bool hideGlow;
  final bool hideContainerColor;
  final double radius;
  final double containerHeight;
  final double fontSize;

  @override
  Widget build(BuildContext context) {
    return GlowContainer(
      glowColor: !hideGlow ? glowColor : null,
      blurRadius: 25,
      offset: const Offset(0, 4),
      child: Stack(
        children: [
          Center(
            child: Container(
              height: context.height * containerHeight / context.height,
              decoration: isEnable
                  ? BoxDecoration(
                      borderRadius: BorderRadius.circular(radius),
                      gradient:
                          hideContainerColor ? null : buttonLinearGradient,
                      color: hideContainerColor
                          ? Colors.transparent
                          : Colors.black.withOpacity(0.8),
                      boxShadow: addShadow
                          ? [
                              BoxShadow(
                                inset: true,
                                color: Colors.white.withOpacity(0.5),
                                blurRadius: 10,
                                offset: const Offset(4, 5),
                              ),
                            ]
                          : [],
                    )
                  : BoxDecoration(
                      borderRadius: BorderRadius.circular(radius),
                      gradient: hideContainerColor ? null : linearGradient,
                      color: hideContainerColor
                          ? null
                          : Colors.black.withOpacity(0.5),
                      boxShadow: isFromWallet
                          ? [
                              BoxShadow(
                                inset: true,
                                color: Colors.white.withOpacity(0.5),
                                blurRadius: 10,
                                offset: const Offset(4, 5),
                              ),
                            ]
                          : [],
                    ),
              child: ElevatedButton(
                onPressed: isEnable ? onTap : null,
                style: ElevatedButton.styleFrom(
                  // padding: const EdgeInsets.only(right: 12),
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  shape: radius > 200
                      ? const CircleBorder()
                      : RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(radius),
                        ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (preFix != null) preFix!,
                    if (preFix != null)
                      const SizedBox(
                        width: 8,
                      ),
                    FittedBox(
                      child: Text(
                        title,
                        textAlign: TextAlign.center,
                        style: context.medium?.copyWith(
                          fontSize: fontSize,
                          color: isEnable ? kWhite : kWhite.withOpacity(0.5),
                        ),
                      ),
                    ),
                    if (trailing != null)
                      const SizedBox(
                        width: 8,
                      ),
                    if (trailing != null) trailing!,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
