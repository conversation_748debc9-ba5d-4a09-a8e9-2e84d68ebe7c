import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/responsive_font.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';

class InfoChip extends StatelessWidget {
  const InfoChip({
    super.key,
    required this.icon,
    required this.value,
    this.color = Colors.white,
    this.width = 120,
  });

  final String icon;
  final String value;
  final Color color;
  final double width;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      padding: EdgeInsets.symmetric(
        horizontal: 10,
        vertical: context.isDesktop ? 6 : 10,
      ),
      decoration: BoxDecoration(
        color: context.isDesktop
            ? kBorderColor.withOpacity(0.12)
            : kWhite.withOpacity(0.05), //kWhite.withOpacity(0.05),
        borderRadius: BorderRadius.circular(15),
        // border: Border.all(color: kBorderColor.withOpacity(0.6), width: 0.14),
        boxShadow: const [
          // BoxShadow(
          //   color: Colors.black12,
          //   blurRadius: 4.8,
          //   spreadRadius: 5.7,
          //   offset: Offset(2, 1.5),
          // ),
        ],
        border: Border.all(color: kWhite.withOpacity(0.2), width: 0.5),
      ),
      child: Row(
        // mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            icon,
            height: 14,
            width: 14,
          ),
          Space.horizontal(context.isDesktopExtraSmall ? 5 : 7),
          Text(
            value,
            style: context.normal!.copyWith(
              color: color,
              fontSize: context.responsiveFontSize(
                  context.isDesktop || context.isDesktopSmall ? 10 : 15),
            ),
          ),
        ],
      ),
    );
  }
}
