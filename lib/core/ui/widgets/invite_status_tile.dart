import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_glow/flutter_glow.dart' hide BoxDecoration, BoxShadow;
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/core/utils/box_shadow/flutter_inset_box_decoration.dart';
import 'package:nexqloud/core/utils/box_shadow/flutter_inset_box_shadow.dart';

class InviteStatusTile extends StatelessWidget {
  const InviteStatusTile({
    super.key,
    required this.title,
    required this.value,
    required this.unit,
    required this.progress,
  });
  final String title;
  final String value;
  final String unit;
  final double progress;

  @override
  Widget build(BuildContext context) {
    var progressValue = progress;
    if (progressValue < 0) {
      progressValue = 0;
    } else if (progressValue > 10 && progressValue < 100) {
      progressValue = progressValue - 5;
    } else if (progressValue > 100) {
      progressValue = 100;
    }

    return Center(
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.only(
          top: 10,
          bottom: 8,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment:
              MainAxisAlignment.spaceBetween, // Changed to spaceBetween
          children: [
            SizedBox(
              height:
                  context.isMobile ? 14 : 18, // Fixed height for progress bar
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final maxWidth = constraints.maxWidth;
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(15),
                    child: Stack(
                      children: [
                        Container(
                          height: context.isMobile ? 14 : 18,
                          width: maxWidth,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            color: kWhite.withOpacity(0.07),
                            boxShadow: [
                              BoxShadow(
                                inset: true,
                                color: Colors.black.withOpacity(0.35),
                                blurRadius: 5,
                                offset: const Offset(0.05, 3),
                              ),
                            ],
                          ),
                        ),
                        // Progress indicator
                        GlowContainer(
                          glowColor: glowColor,
                          blurRadius: 20,
                          offset: const Offset(0, 0),
                          child: Container(
                            height: context.isMobile ? 14 : 18,
                            // Calculate width as percentage of available space
                            width: maxWidth * (progressValue / 100),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              gradient: linearGradient,
                              boxShadow: [
                                BoxShadow(
                                  inset: true,
                                  color: Colors.white.withOpacity(0.3),
                                  blurRadius: 5,
                                  offset: const Offset(6, 3),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            const Space.vertical(8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: value,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: context.isMobile ? 16 : 18,
                          fontFamily: 'Livvic',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      TextSpan(
                        text: ' $unit',
                        style: const TextStyle(
                          color: Color(0xFFABABAB),
                          fontSize: 14,
                          fontFamily: 'Rubik',
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  title,
                  textAlign: TextAlign.right,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: context.isMobile ? 16 : 18,
                    fontFamily: 'Rubik',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
