import 'dart:math';

import 'package:flutter/material.dart';
import 'package:nexqloud/core/constants/colors.dart';

class AppNeedleGauge extends StatefulWidget {
  const AppNeedleGauge({
    super.key,
    required this.progress,
    this.centerWidget,
    this.size,
    required this.strokeWidth,
    this.showTicks = true,
    this.radiusFactor = 1.0,
  });

  final double progress; // Value between 0 and 100
  final Widget? centerWidget; // Optional widget below the gauge
  final double? size; // Optional size (diameter or width)
  final double strokeWidth; // Thickness of the arcs, required
  final bool showTicks;
  final double radiusFactor; // Factor to control size of the gauge

  @override
  AppNeedleGaugeState createState() => AppNeedleGaugeState();
}

class AppNeedleGaugeState extends State<AppNeedleGauge>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    final progress = widget.progress.clamp(0.0, 100.0);
    _animation = Tween<double>(begin: 0, end: progress).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    )..addListener(() {
        setState(() {});
      });

    _controller.forward();
  }

  @override
  void didUpdateWidget(AppNeedleGauge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      final progress = widget.progress.clamp(0.0, 100.0);
      _animation =
          Tween<double>(begin: _animation.value, end: progress).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Curves.easeInOut,
        ),
      );
      _controller
        ..reset()
        ..forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final gaugeSize =
            widget.size ?? min(constraints.maxWidth, constraints.maxHeight);
        final gaugeHeight = gaugeSize * 0.8;

        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: gaugeSize,
              height: gaugeHeight,
              child: CustomPaint(
                painter: _NeedleGaugePainter(
                  animatedProgress: _animation.value,
                  strokeWidth: widget.strokeWidth,
                  showTicks: widget.showTicks,
                  radiusFactor: widget.radiusFactor,
                ),
              ),
            ),
            const SizedBox(height: 10),
            widget.centerWidget ?? const SizedBox(),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }
}

class _NeedleGaugePainter extends CustomPainter {
  _NeedleGaugePainter({
    required this.animatedProgress,
    required this.strokeWidth,
    this.showTicks = true,
    this.radiusFactor = 1.0,
  });

  final double animatedProgress;
  final double strokeWidth;
  final bool showTicks;
  final double radiusFactor;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 1.3);
    final radius = (min(size.width / 2, size.height / 1.2) - strokeWidth / 2) *
        radiusFactor;
    final rect = Rect.fromCircle(center: center, radius: radius);

    const startAngle = pi * 3 / 4;
    const sweepAngle = pi * 3 / 2;

    // Scaled parameters
    final scaledTicksStartFrom = (strokeWidth / 2 + 5.0) * radiusFactor;
    final tickEndOffset = 12.0 * radiusFactor;
    final needleWidth = 14.0 * radiusFactor;

    // Background arc
    final backgroundPaint = Paint()
      ..color = Colors.white.withOpacity(0.95)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    // Left border (shadow effect)
    final leftBorderPaint = Paint()
      ..color = Colors.black.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth * 0.9
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6)
      ..strokeCap = StrokeCap.round;

    // Progress arc
    final progressPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    const gradient = LinearGradient(
      colors: [gradientColorOne, gradientColorThree],
    );
    progressPaint.shader = gradient.createShader(rect);

    // Draw arcs
    canvas.drawArc(rect, startAngle, sweepAngle, false, backgroundPaint);
// <<<<<<< HEAD

    // // Left border: Slightly offset to emphasize the start (left side)
    // const leftBorderAngleOffset = -0.05; // Small angular offset for left
    // canvas.drawArc(
    //   rect,
    //   startAngle,
    //   sweepAngle + leftBorderAngleOffset,
    //   false,
    //   leftBorderPaint,
    // );

    // Progress arc and glow
// =======
//     const leftBorderAngleOffset = -0.05;
//     canvas.drawArc(
//       rect,
//       startAngle,
//       sweepAngle + leftBorderAngleOffset,
//       false,
//       leftBorderPaint,
//     );
// >>>>>>> a84b037647e287c1a38b988068ded2e176fa9c5e
    final progressSweepAngle = (animatedProgress / 100) * sweepAngle;
    canvas.drawArc(rect, startAngle, progressSweepAngle, false, progressPaint);

    final glowPaint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth + 12
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10)
      ..color = const Color(0x80A1C6FF);
    canvas.drawArc(rect, startAngle, progressSweepAngle, false, glowPaint);

    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.085)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10);
    canvas.drawArc(rect, startAngle, sweepAngle, false, shadowPaint);

    // Top White (shadow effect)
    final toNegativeEffectPaint = Paint()
      ..color = Colors.white.withOpacity(0.13)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth * 0.9
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6)
      ..strokeCap = StrokeCap.round;
    final gradientShadow = LinearGradient(
      colors: [
        kWhite,
        kWhite,
        kWhite.withOpacity(0.3),
        kWhite.withOpacity(0.5),
        kWhite,
      ],
    );
    leftBorderPaint.shader = gradientShadow.createShader(rect);

    canvas.drawArc(
      rect,
      startAngle,
      progressSweepAngle,
      false,
      toNegativeEffectPaint,
    );

    // Ticks
    if (showTicks) {
      for (var i = 0; i <= 20; i++) {
        final angle = startAngle + (i / 20) * sweepAngle;
        final isMajorTick = i % 4 == 0;
        final tickLength = isMajorTick ? tickEndOffset : tickEndOffset / 1.5;
        final startX = center.dx + (radius - scaledTicksStartFrom) * cos(angle);
        final startY = center.dy + (radius - scaledTicksStartFrom) * sin(angle);
        final endX = center.dx +
            (radius - scaledTicksStartFrom - tickLength) * cos(angle);
        final endY = center.dy +
            (radius - scaledTicksStartFrom - tickLength) * sin(angle);
        final tickPaint = Paint()
          ..color = Colors.white.withOpacity(0.8)
          ..strokeWidth = (isMajorTick ? 2.0 : 1.5) * radiusFactor
          ..strokeCap = StrokeCap.round;
        canvas.drawLine(Offset(startX, startY), Offset(endX, endY), tickPaint);
      }
    }

    // Needle
    final needleAngle = startAngle + (animatedProgress / 100) * sweepAngle;
    final needleLength = radius - scaledTicksStartFrom - tickEndOffset;

    final tipX = center.dx + needleLength * cos(needleAngle);
    final tipY = center.dy + needleLength * sin(needleAngle);

    final baseLeftX = center.dx + (needleWidth / 2) * cos(needleAngle + pi / 2);
    final baseLeftY = center.dy + (needleWidth / 2) * sin(needleAngle + pi / 2);

    final baseRightX =
        center.dx + (needleWidth / 2) * cos(needleAngle - pi / 2);
    final baseRightY =
        center.dy + (needleWidth / 2) * sin(needleAngle - pi / 2);

    final needlePath = Path()
      ..moveTo(tipX, tipY)
      ..lineTo(baseLeftX, baseLeftY)
      ..arcToPoint(
        Offset(baseRightX, baseRightY),
        radius: Radius.circular(needleWidth / 2),
        clockwise: false,
      )
      ..close();

    final needlePaint = Paint()
      ..color = const Color(0xFF8052FF)
      ..style = PaintingStyle.fill;

    canvas.drawPath(needlePath, needlePaint);

    // Center circle
    final centerCirclePaint = Paint()..color = Colors.white;
    final centerCircleRadius = 8 * radiusFactor;
    canvas.drawCircle(center, centerCircleRadius, centerCirclePaint);
  }

  @override
  bool shouldRepaint(covariant _NeedleGaugePainter oldDelegate) {
    return animatedProgress != oldDelegate.animatedProgress ||
        strokeWidth != oldDelegate.strokeWidth ||
        radiusFactor != oldDelegate.radiusFactor ||
        showTicks != oldDelegate.showTicks;
  }
}
