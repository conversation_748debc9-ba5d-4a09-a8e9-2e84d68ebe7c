import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:nexqloud/core/constants/api_constants.dart';

class NetworkServices {
  NetworkServices._();

  static Future fetchServersTableData() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri/admin/create'),
        // body: jsonEncode({}),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          'x-api-key': apiKeyProd,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      // Network error: $e
      return {};
    }
  }
}
