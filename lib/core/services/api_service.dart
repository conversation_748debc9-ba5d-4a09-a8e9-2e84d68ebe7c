import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:nexqloud/core/constants/api_constants.dart';

class ApiService {
  ApiService({
    this.baseUrl = '$baseUriFromEnv/',
    this.apiKey,
    this.authToken,
  });
  final String baseUrl;
  final String? apiKey;
  String? authToken;

  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };

    if (authToken != null) {
      headers['Authorization'] = 'Bearer $authToken';
    }

    if (apiKey != null) {
      headers['x-api-key'] = apiKey!;
    }

    return headers;
  }

  void _handleError(http.Response response) {
    final errorBody = json.decode(response.body);
    throw ApiException(
      errorBody['message'] ?? 'Unknown error',
      response.statusCode,
      response.body,
    );
  }

  // Generic GET method with type safety
  Future<dynamic> get(
    String endpoint,
  ) async {
    try {
      final url = '$baseUrl$endpoint';
      final response = await http
          .get(
            Uri.parse(url),
            headers: _headers,
          )
          .timeout(const Duration(seconds: 30));
      // print(json.decode(response.body));
      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return jsonResponse;
      }
      _handleError(response);
    } catch (e) {
      throw ApiException('Unknown error', 0, e.toString());
    }
    throw ApiException('Unknown error', 0, 'Something went wrong');
  }

  // Generic POST method with type safety
  Future<Map<String, dynamic>> post(
    String endpoint,
    dynamic dto, {
    bool useDefaultBaseUrl = true,
  }) async {
    try {
      final requestBody = json.encode(dto);
      final url = useDefaultBaseUrl ? '$baseUrl$endpoint' : endpoint;
      // Making POST request to $url
      final response = await http.post(
        Uri.parse(url),
        headers: _headers,
        body: requestBody,
      );
      // print('Response: ${response.body}');

      if (response.statusCode == 201 || response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return jsonResponse;
      }
      _handleError(response);
    } catch (e) {
      throw ApiException('Unknown error', 0, e.toString());
    }
    throw ApiException('Unknown error', 0, 'Something went wrong');
  }

  // Generic PUT method with type safety
  Future<Map<String, dynamic>> put(
    String endpoint,
    dynamic dto,
  ) async {
    try {
      final response = await http
          .put(
            Uri.parse('$baseUrl$endpoint'),
            headers: _headers,
            body: json.encode(dto.toJson()),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return jsonResponse;
      }
      _handleError(response);
    } catch (e) {
      throw ApiException('Unknown error', 0, e.toString());
    }
    throw ApiException('Unknown error', 0, 'Something went wrong');
  }

  // Delete method
  Future<void> delete(String endpoint) async {
    try {
      final response = await http
          .delete(
            Uri.parse('$baseUrl$endpoint'),
            headers: _headers,
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 204 || response.statusCode == 200) {
        return;
      }
      _handleError(response);
    } catch (e) {
      throw ApiException('Unknown error', 0, e.toString());
    }
    throw ApiException('Unknown error', 0, 'Something went wrong');
  }

  void updateAuthToken(String token) {
    authToken = token;
  }
}

class ApiException implements Exception {
  ApiException(this.message, this.statusCode, this.response);
  final String message;
  final int statusCode;
  final String response;

  @override
  String toString() {
    return 'ApiException: $message (Status Code: $statusCode)\nResponse: $response';
  }
}
