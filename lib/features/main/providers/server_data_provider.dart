import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:nexqloud/core/extensions/log.dart';
import 'package:nexqloud/core/services/api_service.dart';
import 'package:nexqloud/features/main/models/device_model.dart';
import 'package:nexqloud/features/main/models/server_info.dart';
import 'package:nexqloud/features/main/models/server_model.dart';
import 'package:nexqloud/features/main/models/system_metrics.dart';
import 'package:nexqloud/features/main/repositories/network_map_repository.dart';

class ServerDataProvider with ChangeNotifier {
  ServerDataProvider({required this.mapRepository});

  final NetworkMapRepository mapRepository;

  List<ServerInfo> _serversWithLatLng = <ServerInfo>[];
  List<ServerModel> _tableData = <ServerModel>[];
  bool _isRowSelected = false;
  bool _isLoading = false;
  bool _isTableDataLoading = false;
  int pageNumber = 0;
  int numberOfPages = 0;
  DeviceModel fetchedServerData = DeviceModel.empty();
  bool _isMapExpanded = false;

  bool get isMapExpanded => _isMapExpanded;
  List<ServerInfo> get serversWithLatLng => _serversWithLatLng;
  List<ServerModel> get tableData => _tableData;
  bool get isRowSelected => _isRowSelected;
  bool get isLoading => _isLoading;
  bool get isTableDateLoading => _isTableDataLoading;

  set isRowSelected(bool value) {
    _isRowSelected = value;
    notifyListeners();
  }

  ServerInfo? _serverModel;

  ServerInfo? get serverModel => _serverModel;

  set serverModel(ServerInfo? value) {
    _serverModel = value;
  }

  void onInit() async {
    try {
      await _fetchServersData();
      await fetchServersDataFiltered();
      await fetchSystemMetrics();
    } catch (e) {
      e.printError();
    }
  }

  Future<void> _fetchServersData() async {
    try {
      _isLoading = true;
      notifyListeners();
      final data = await mapRepository.fetchServers();
      _serversWithLatLng = data;
    } on ApiException catch (e) {
      e.message.printError();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> fetchServersDataFiltered({
    int page = 1,
    int limit = 10,
    bool? online = true,
    String? region,
    String? nftId,
  }) async {
    try {
      _isTableDataLoading = true;
      notifyListeners();
      final data = await mapRepository.fetchTableDataFiltered(
        page: page,
        limit: limit,
        online: online,
        region: region,
        nftId: nftId,
      );

      _tableData = data.servers;

      _getNumberOfPages(data.total);
      _isTableDataLoading = false;
    } on ApiException catch (e) {
      e.message.printError();
    } finally {
      _isTableDataLoading = false;
      notifyListeners();
    }
  }

  SystemMetrics systemMetrics = SystemMetrics.empty();

  Timer? systemMetricsTimer;
  Timer? singleServerDataTimer;

  Future<void> fetchSystemMetrics() async {
    if (systemMetrics.totalMemory == 0) {
      systemMetrics = SystemMetrics(
        totalDevices: 0,
        totalDiskSpace: 0,
        totalMemory: 0,
        totalMemoryUsed: 100,
        cpuUtilization: 100,
        totalCPU: 100,
        totalMemoryUsagePercentage: 100,
        networkInbound: 100,
        networkInboundMax: 100,
        networkOutbound: 100,
        networkOutboundMax: 100,
        ramUtilization: 100,
        diskUsage: 0,
        totalGPUs: 0,
        devicesOnline: 0,
        diskRead: 100,
        diskReadMax: 100,
        diskWrite: 100,
        diskWriteMax: 100,
        uptimePercentage: 100,
        diskIO: 0,
        percentages: MetricsPercentages(
          cpuUtilizationPercentile: 100,
          ramUtilizationPercentile: 100,
          diskReadPercentile: 100,
          diskWritePercentile: 100,
          networkInboundPercentile: 100,
          networkOutboundPercentile: 100,
        ),
        diskAvgBandwidth: 0,
        networkAvgBandwidth: 0,
        totalVCPUCores: 0,
      );
      notifyListeners();
      await Future.delayed(const Duration(milliseconds: 500));
      await _fetchSystemMetrics();
    }
    systemMetricsTimer?.cancel();
    systemMetricsTimer = Timer.periodic(
      const Duration(seconds: 10),
      (_) async {
        await _fetchSystemMetrics();
      },
    );
  }

  Future<void> _fetchSystemMetrics() async {
    try {
      systemMetrics = await mapRepository.fetchSystemMetrics();
    } on ApiException catch (e) {
      e.message.printError();
    } finally {
      notifyListeners();
    }
  }

  Future<void> fetchSingleServerData(String hostName) async {
    if (hostName.isEmpty) return;
    await _fetchSingleServerData(hostName);
  }

  Future<void> fetchSingleServerDataViaServerId(String serverId) async {
    if (serverId.isEmpty) return;
    await _fetchSingleServerDataViaServerId(serverId);
  }

  bool isFetchingSingleServerData = false;

  Future<void> _fetchSingleServerData(String hostName) async {
    try {
      isFetchingSingleServerData = true;
      notifyListeners();

      fetchedServerData = await mapRepository.fetchSingleServerData(hostName);
    } on ApiException catch (e) {
      isFetchingSingleServerData = false;
      notifyListeners();
      e.message.printError();
    } finally {
      isFetchingSingleServerData = false;
      notifyListeners();
    }
  }

  Future<void> _fetchSingleServerDataViaServerId(String serverId) async {
    try {
      isFetchingSingleServerData = true;
      notifyListeners();

      fetchedServerData =
          await mapRepository.fetchSingleServerDataViaServerId(serverId);
    } on ApiException catch (e) {
      isFetchingSingleServerData = false;
      notifyListeners();
      e.message.printError();
    } finally {
      isFetchingSingleServerData = false;
      notifyListeners();
    }
  }

  void toggleMapExpand(bool value) {
    // add guard clause
    if (_isMapExpanded == value) return;
    _isMapExpanded = value;
    notifyListeners();
  }

  void _getNumberOfPages(int total) {
    numberOfPages = (total / 10).ceil();
  }

  // List<String> getContinentList() {
  //   return serversWithLatLng.map((e) => e.continent).toSet().toList();
  // }

  // // find one server data from a continent
  // ServerModel findContinentServer(String continent) {
  //   return serversWithLatLng.firstWhere(
  //     (element) => element.continent == continent,
  //     orElse: () => ServerModel.empty(),
  //   );
  // }

  // List<String> getCountryList() {
  //   return serversWithLatLng.map((e) => e.country).toSet().toList();
  // }

  // // find country based markers
  // List<ServerModel> findCountry(String country) {
  //   return serversWithLatLng
  //       .where((element) => element.country == country)
  //       .toList();
  // }

  // List<String> getRegionList() {
  //   return serversWithLatLng.map((e) => e.region).toSet().toList();
  // }

  // // find region based markers
  // List<ServerModel> findRegion(String region) {
  //   return serversWithLatLng
  //       .where((element) => element.region == region)
  //       .toList();
  // }

  // find countries in a continent
  // List<String> findCountryInContinent(String continent) {
  //   return serversWithLatLng
  //       .where((element) => element.continent == continent)
  //       .map((e) => e.country)
  //       .toSet()
  //       .toList();
  // }
}
