// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:nexqloud/features/main/models/gpu_info.dart';
import 'package:nexqloud/features/main/models/server_provider.dart';

class ServerModel {
  ServerModel({
    // Updated constructor name
    required this.serverName,
    required this.serverId,
    required this.model,
    required this.latitude,
    required this.longitude,
    required this.continent,
    required this.region,
    required this.country,
    required this.city,
    required this.status,
    required this.orderStatus,
    required this.createdAt,
    required this.updatedAt,
    required this.cpu,
    required this.memory,
    required this.memoryTotal,
    required this.memoryUsed,
    required this.diskUsage,
    required this.gpu,
    this.provider,
    required this.isOnline,
    required this.cloudScore,
    required this.uptime,
    required this.licenseId,
    required this.licenseLinked,
    this.nft,
  });

  // create empty factory
  factory ServerModel.empty() => ServerModel(
        serverName: '',
        serverId: '',
        model: '',
        latitude: 0,
        longitude: 0,
        continent: '',
        region: '',
        country: '',
        city: '',
        status: false,
        orderStatus: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        cpu: 0,
        memory: 0,
        memoryTotal: 0,
        memoryUsed: 0,
        diskUsage: 0,
        gpu: [],
        isOnline: false,
        cloudScore: 0,
        uptime: '',
        licenseId: '',
        licenseLinked: false,
      );

  factory ServerModel.fromJson(Map<String, dynamic> json) => ServerModel(
        // Updated factory name
        serverName: json['serverName']?.toString() ?? '',
        serverId: json['serverId']?.toString() ?? '',
        model: json['model']?.toString() ?? '',
        latitude: _parseDouble(json['latitude']) ?? 0.0,
        longitude: _parseDouble(json['longitude']) ?? 0.0,
        continent: json['continent']?.toString() ?? '',
        region: json['region']?.toString() ?? '',
        country: json['country']?.toString() ?? '',
        city: json['city']?.toString() ?? '',
        status: json['status'] as bool? ?? false,
        orderStatus: json['orderStatus']?.toString() ?? '',
        createdAt: json['createdAt'] != null
            ? DateTime.tryParse(json['createdAt'].toString()) ?? DateTime.now()
            : DateTime.now(),
        updatedAt: json['updatedAt'] != null
            ? DateTime.tryParse(json['updatedAt'].toString()) ?? DateTime.now()
            : DateTime.now(),
        cpu: _parseDouble(json['cpu']) ?? 0.0,
        memory: _parseDouble(json['memory']) ?? 0.0,
        memoryTotal: (json['memoryTotal'] as num?)?.toDouble() ?? 0.0,
        memoryUsed: (json['memoryUsed'] as num?)?.toDouble() ?? 0.0,
        diskUsage: _parseDouble(json['diskUsage']) ?? 0.0,
        gpu: (json['gpu'] as List<dynamic>?)
                ?.map((e) => GpuInfo.fromJson(e as Map<String, dynamic>))
                .toList() ??
            [],
        provider: json['provider'] != null
            ? ServerProvider.fromJson(json['provider'] as Map<String, dynamic>)
            : null,
        isOnline: json['isOnline'] as bool? ?? false,
        cloudScore: (json['cloudScore'] as num?)?.toDouble() ?? 0.0,
        uptime: ((json['uptime'] as num?)?.toDouble() ?? 0.0)
            .toString(), //json['uptime']?.toString() ?? '0.00',
        licenseId: json['licenseId']?.toString() ?? '',
        licenseLinked: json['licenseLinked'] as bool? ?? false,
        nft: json['nft'] != null
            ? NFT.fromJson(json['nft'] as Map<String, dynamic>)
            : null,
      ); // Renamed from Server to ServerModel
  final String serverName;
  final String serverId;
  final String model;
  final double latitude;
  final double longitude;
  final String continent;
  final String region;
  final String country;
  final String city;
  final bool status;
  final String orderStatus;
  final DateTime createdAt;
  final DateTime updatedAt;
  final double cpu;
  final double memory;
  final double memoryTotal; // Changed from int to double
  final double memoryUsed; // Changed from int to double
  final double diskUsage;
  final List<GpuInfo>? gpu;
  final ServerProvider? provider;
  final bool isOnline;
  final double cloudScore; // Changed from int to double
  final String uptime;
  final String licenseId;
  final bool licenseLinked;
  final NFT? nft;

  Map<String, dynamic> toJson() => {
        'serverName': serverName,
        'hostName': serverId,
        'model': model,
        'latitude': latitude,
        'longitude': longitude,
        'continent': continent,
        'region': region,
        'country': country,
        'city': city,
        'status': status,
        'orderStatus': orderStatus,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'cpu': cpu,
        'memory': memory,
        'memoryTotal': memoryTotal,
        'memoryUsed': memoryUsed,
        'diskUsage': diskUsage,
        'gpu': gpu?.map((e) => e.toJson()).toList(),
        'provider': provider?.toJson(),
        'isOnline': isOnline,
        'cloudScore': cloudScore,
        'uptime': uptime,
        'licenseId': licenseId,
        'licenseLinked': licenseLinked,
        'nft': nft?.toJson(),
      };

  static double? _parseDouble(dynamic value) {
    if (value == null || value.toString().isEmpty) return null;
    if (value is num) return value.toDouble();
    try {
      return double.parse(value.toString());
    } catch (e) {
      return null;
    }
  }

  String get nftImage =>
      nft?.imageUrl ??
      'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQwAvCWqBBeM7ut62kxZT9q0Tc4LnKafNu4M8yEZSl1w_tM9YpSv-ber3mKcpU6khAtB5g&usqp=CAU';

  String get nftTokenId {
    final id = nft?.tokenId.toString() ?? '000000';
    return id.padLeft(6, '0');
  }

  @override
  String toString() {
    return 'ServerModel(serverName: $serverName, hostName: $serverId, model: $model, latitude: $latitude, longitude: $longitude, continent: $continent, region: $region, country: $country, city: $city, status: $status, orderStatus: $orderStatus, createdAt: $createdAt, updatedAt: $updatedAt, cpu: $cpu, memory: $memory, memoryTotal: $memoryTotal, memoryUsed: $memoryUsed, diskUsage: $diskUsage, gpu: $gpu, provider: $provider, isOnline: $isOnline, cloudScore: $cloudScore, uptime: $uptime, licenseId: $licenseId, licenseLinked: $licenseLinked, nft: $nft)';
  }
}

class NFT {
  NFT({
    required this.tokenId,
    required this.imageUrl,
  });

  // create empty factory
  factory NFT.empty() => NFT(
        tokenId: 1063,
        imageUrl:
            'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQwAvCWqBBeM7ut62kxZT9q0Tc4LnKafNu4M8yEZSl1w_tM9YpSv-ber3mKcpU6khAtB5g&usqp=CAU',
      );

  factory NFT.fromJson(Map<String, dynamic> json) => NFT(
        tokenId: (json['tokenId'] as num?)?.toDouble() ?? 0.0,
        imageUrl: json['imageUrl']?.toString() ?? '',
      );

  final double tokenId; // Changed from int to double
  final String imageUrl;

  Map<String, dynamic> toJson() => {
        'tokenId': tokenId,
        'imageUrl': imageUrl,
      };
}
