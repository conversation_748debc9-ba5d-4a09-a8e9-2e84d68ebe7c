class SystemMetrics {
  SystemMetrics({
    required this.totalDevices,
    required this.totalDiskSpace,
    required this.totalMemory,
    required this.totalMemoryUsed,
    required this.cpuUtilization,
    required this.totalCPU,
    required this.totalMemoryUsagePercentage,
    required this.networkInbound,
    required this.networkInboundMax,
    required this.networkOutbound,
    required this.networkOutboundMax,
    required this.ramUtilization,
    required this.diskUsage,
    required this.totalGPUs,
    required this.devicesOnline,
    required this.diskRead,
    required this.diskReadMax,
    required this.diskWrite,
    required this.diskWriteMax,
    required this.uptimePercentage,
    required this.diskIO,
    required this.percentages,
    required this.diskAvgBandwidth,
    required this.networkAvgBandwidth,
    required this.totalVCPUCores,
  });

  factory SystemMetrics.empty() {
    return SystemMetrics(
      totalDevices: 0,
      totalDiskSpace: 0,
      totalMemory: 0,
      totalMemoryUsed: 0,
      cpuUtilization: 0,
      totalCPU: 0,
      totalMemoryUsagePercentage: 0,
      networkInbound: 0,
      networkInboundMax: 0,
      networkOutbound: 0,
      networkOutboundMax: 0,
      ramUtilization: 0,
      diskUsage: 0,
      totalGPUs: 0,
      devicesOnline: 0,
      diskRead: 0,
      diskReadMax: 0,
      diskWrite: 0,
      diskWriteMax: 0,
      uptimePercentage: 0,
      diskIO: 0,
      percentages: MetricsPercentages.empty(),
      diskAvgBandwidth: 0,
      networkAvgBandwidth: 0,
      totalVCPUCores: 0,
    );
  }

  factory SystemMetrics.fromJson(Map<String, dynamic> json) {
    return SystemMetrics(
      totalDevices: _parseDouble(json['totalDevices']),
      totalDiskSpace: _parseDouble(json['totalDiskSpace']),
      totalMemory: _parseDouble(json['totalMemory']),
      totalMemoryUsed: _parseDouble(json['totalMemoryUsed']),
      cpuUtilization: _parseDouble(json['cpuUtilization']),
      totalCPU: _parseDouble(json['totalCPU']),
      totalMemoryUsagePercentage:
          _parseDouble(json['totalMemoryUsagePercentage']),
      networkInbound: _parseDouble(json['networkInbound']),
      networkInboundMax: _parseDouble(json['networkInboundMax']),
      networkOutbound: _parseDouble(json['networkOutbound']),
      networkOutboundMax: _parseDouble(json['networkOutboundMax']),
      ramUtilization: _parseDouble(json['ramUtilization']),
      diskUsage: _parseDouble(json['diskUsage']),
      totalGPUs: _parseDouble(json['totalGPUs']),
      devicesOnline: _parseDouble(json['devicesOnline']),
      diskRead: _parseDouble(json['diskRead']),
      diskReadMax: _parseDouble(json['diskReadMax']),
      diskWrite: _parseDouble(json['diskWrite']),
      diskWriteMax: _parseDouble(json['diskWriteMax']),
      uptimePercentage: _parseDouble(json['uptimePercentage']),
      diskIO: _parseDouble(json['diskIO']),
      percentages: MetricsPercentages.fromJson(json['percentages']),
      diskAvgBandwidth: _parseDouble(json['diskAvgBandwidth']),
      networkAvgBandwidth: _parseDouble(json['networkAvgBandwidth']),
      totalVCPUCores: _parseDouble(json['totalVCPUCores']),
    );
  }

  final double totalDevices;
  final double totalDiskSpace;
  final double totalMemory;
  final double totalMemoryUsed;
  final double cpuUtilization;
  final double totalCPU;
  final double totalMemoryUsagePercentage;
  final double networkInbound;
  final double networkInboundMax;
  final double networkOutbound;
  final double networkOutboundMax;
  final double ramUtilization;
  final double diskUsage;
  final double totalGPUs;
  final double devicesOnline;
  final double diskRead;
  final double diskReadMax;
  final double diskWrite;
  final double diskWriteMax;
  final double uptimePercentage;
  final double diskIO;
  final MetricsPercentages percentages;
  final double diskAvgBandwidth;
  final double networkAvgBandwidth;
  final double totalVCPUCores;

  Map<String, dynamic> toJson() {
    return {
      'totalDevices': totalDevices,
      'totalDiskSpace': totalDiskSpace,
      'totalMemory': totalMemory,
      'totalMemoryUsed': totalMemoryUsed,
      'cpuUtilization': cpuUtilization,
      'totalCPU': totalCPU,
      'totalMemoryUsagePercentage': totalMemoryUsagePercentage,
      'networkInbound': networkInbound,
      'networkInboundMax': networkInboundMax,
      'networkOutbound': networkOutbound,
      'networkOutboundMax': networkOutboundMax,
      'ramUtilization': ramUtilization,
      'diskUsage': diskUsage,
      'totalGPUs': totalGPUs,
      'devicesOnline': devicesOnline,
      'diskRead': diskRead,
      'diskReadMax': diskReadMax,
      'diskWrite': diskWrite,
      'diskWriteMax': diskWriteMax,
      'uptimePercentage': uptimePercentage,
      'diskIO': diskIO,
      'percentages': percentages.toJson(),
      'diskAvgBandwidth': diskAvgBandwidth,
      'networkAvgBandwidth': networkAvgBandwidth,
      'totalVCPUCores': totalVCPUCores,
    };
  }
}

class MetricsPercentages {
  MetricsPercentages({
    required this.cpuUtilizationPercentile,
    required this.ramUtilizationPercentile,
    required this.diskReadPercentile,
    required this.diskWritePercentile,
    required this.networkInboundPercentile,
    required this.networkOutboundPercentile,
  });

  factory MetricsPercentages.empty() {
    return MetricsPercentages(
      cpuUtilizationPercentile: 0,
      ramUtilizationPercentile: 0,
      diskReadPercentile: 0,
      diskWritePercentile: 0,
      networkInboundPercentile: 0,
      networkOutboundPercentile: 0,
    );
  }

  factory MetricsPercentages.fromJson(Map<String, dynamic> json) {
    return MetricsPercentages(
      cpuUtilizationPercentile: _parseDouble(json['cpuUtilizationPercentile']),
      ramUtilizationPercentile: _parseDouble(json['ramUtilizationPercentile']),
      diskReadPercentile: _parseDouble(json['diskReadPercentile']),
      diskWritePercentile: _parseDouble(json['diskWritePercentile']),
      networkInboundPercentile: _parseDouble(json['networkInboundPercentile']),
      networkOutboundPercentile:
          _parseDouble(json['networkOutboundPercentile']),
    );
  }

  final double cpuUtilizationPercentile;
  final double ramUtilizationPercentile;
  final double diskReadPercentile;
  final double diskWritePercentile;
  final double networkInboundPercentile;
  final double networkOutboundPercentile;

  Map<String, dynamic> toJson() {
    return {
      'cpuUtilizationPercentile': cpuUtilizationPercentile,
      'ramUtilizationPercentile': ramUtilizationPercentile,
      'diskReadPercentile': diskReadPercentile,
      'diskWritePercentile': diskWritePercentile,
      'networkInboundPercentile': networkInboundPercentile,
      'networkOutboundPercentile': networkOutboundPercentile,
    };
  }
}

double _parseDouble(dynamic value, {double defaultValue = 0.0}) {
  if (value == null || (value is String && value.isEmpty)) {
    return defaultValue;
  }
  if (value is num) {
    return value.toDouble();
  }
  return double.tryParse(value.toString()) ?? defaultValue;
}
