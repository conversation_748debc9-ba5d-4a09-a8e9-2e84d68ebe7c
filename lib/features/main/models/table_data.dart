import 'package:nexqloud/features/main/models/server_model.dart';

// models/table_data.dart

class TableData {
  const TableData({
    required this.servers,
    required this.total,
  });

  factory TableData.fromJson(Map<String, dynamic> json) {
    return TableData(
      servers: (json['servers'] as List<dynamic>)
          .map(
            (serverJson) =>
                ServerModel.fromJson(serverJson as Map<String, dynamic>),
          )
          .toList(),
      total: json['total'] as int,
    );
  }
  final List<ServerModel> servers;
  final int total;

  @override
  List<Object?> get props => [servers, total];

  Map<String, dynamic> toJson() {
    return {
      'servers': servers.map((server) => server.toJson()).toList(),
      'total': total,
    };
  }

  @override
  String toString() => 'TableData(servers: $servers, total: $total)';
}
