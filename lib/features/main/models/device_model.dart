import 'package:nexqloud/features/main/models/server_model.dart';

class DeviceModel {
  DeviceModel({
    this.id = '',
    // this.hostName = '',
    this.deviceId = '',
    LocationInfo? location,
    this.diskUsage = 0.0,
    this.cpu = 0.0,
    this.memoryUsagePercentage = 0.0,
    this.memoryUsed = 0.0,
    this.memoryTotal = 0.0,
    this.diskRead = 0.0,
    this.diskWrite = 0.0,
    List<GpuInfo>? gpu,
    this.networkInbound = 0.0,
    this.networkOutbound = 0.0,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.uptimePercentage = 0.0,
    this.isOnline = false,
    UtilizationPercentages? percentages,
    required this.cloudScore,
    required this.licenseId,
    this.nft,
    this.model = 'Unknown',
    this.temperature = 0.0,
  })  : location = location ?? const LocationInfo(),
        gpu = gpu ?? const [],
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now(),
        percentages = percentages ?? const UtilizationPercentages();

  // create empty factory
  factory DeviceModel.empty() => DeviceModel(
        cloudScore: 0,
        licenseId: '',
      );

  factory DeviceModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return DeviceModel(
        cloudScore: 0,
        licenseId: '',
      );
    }

    return DeviceModel(
      id: json['_id']?.toString() ?? '',
      // hostName: json['hostName']?.toString() ?? '',
      deviceId: json['deviceId']?.toString() ?? '',
      location:
          LocationInfo.fromJson(json['location'] as Map<String, dynamic>?),
      diskUsage: (json['diskUsage'] as num?)?.toDouble() ?? 0.0,
      cpu: (json['cpu'] as num?)?.toDouble() ?? 0.0,
      memoryUsagePercentage:
          (json['memoryUsagePercentage'] as num?)?.toDouble() ?? 0.0,
      memoryUsed: (json['memoryUsed'] as num?)?.toDouble() ?? 0.0,
      memoryTotal: (json['memoryTotal'] as num?)?.toDouble() ?? 0.0,
      diskRead: (json['diskRead'] as num?)?.toDouble() ?? 0.0,
      diskWrite: (json['diskWrite'] as num?)?.toDouble() ?? 0.0,
      gpu: (json['gpu'] as List<dynamic>?)
              ?.map((e) => GpuInfo.fromJson(e as Map<String, dynamic>?))
              .toList() ??
          const [],
      model: json['model']?.toString() ?? 'Unknown',
      networkInbound: (json['networkInbound'] as num?)?.toDouble() ?? 0.0,
      networkOutbound: (json['networkOutbound'] as num?)?.toDouble() ?? 0.0,
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
      uptimePercentage: (json['uptimePercentage'] as num?)?.toDouble() ?? 0.0,
      isOnline: json['isOnline'] as bool? ?? false,
      percentages: UtilizationPercentages.fromJson(
        json['percentages'] as Map<String, dynamic>?,
      ),
      cloudScore: (json['cloudScore'] as num?)?.toDouble() ?? 0.0,
      licenseId: json['licenseId']?.toString() ?? '',
      nft: json['nft'] != null
          ? NFT.fromJson(json['nft'] as Map<String, dynamic>)
          : null,
      temperature: (json['temperature'] as num?)?.toDouble() ?? 0.0,
    );
  }
  final String id;
  // final String hostName;
  final String deviceId;
  final LocationInfo location;
  final double diskUsage;
  final double cpu;
  final double memoryUsagePercentage;
  final double memoryUsed;
  final double memoryTotal;
  final double diskRead;
  final double diskWrite;
  final List<GpuInfo> gpu;
  final double networkInbound;
  final double networkOutbound;
  final DateTime createdAt;
  final DateTime updatedAt;
  final double uptimePercentage;
  final bool isOnline;
  final UtilizationPercentages percentages;
  final String licenseId;
  final NFT? nft;
  final double cloudScore; // Changed from int to double
  final String model;
  final double temperature;
  Map<String, dynamic> toJson() => {
        '_id': id,
        // 'hostName': hostName,
        'deviceId': deviceId,
        'location': location.toJson(),
        'diskUsage': diskUsage,
        'cpu': cpu,
        'memoryUsagePercentage': memoryUsagePercentage,
        'memoryUsed': memoryUsed,
        'memoryTotal': memoryTotal,
        'diskRead': diskRead,
        'diskWrite': diskWrite,
        'gpu': gpu.map((e) => e.toJson()).toList(),
        'networkInbound': networkInbound,
        'networkOutbound': networkOutbound,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'uptimePercentage': uptimePercentage,
        'isOnline': isOnline,
        'percentages': percentages.toJson(),
        'cloudScore': cloudScore,
        'licenseId': licenseId,
        'nft': nft?.toJson(),
        'model': model,
        'temperature': temperature,
      };

  String get nftTokenId {
    final id = nft?.tokenId.toString() ?? '000000'; //'1063';
    return id.padLeft(6, '0');
  }
}

class LocationInfo {
  const LocationInfo({
    this.latitude = 0.0,
    this.longitude = 0.0,
    this.timeZone = '',
    this.country = '',
    this.city = '',
    this.continent = '',
    this.accuracyRadius = 0.0,
    this.region = '',
  });

  factory LocationInfo.fromJson(Map<String, dynamic>? json) {
    if (json == null) return const LocationInfo();

    return LocationInfo(
      latitude: (json['latitude'] as num?)?.toDouble() ?? 0.0,
      longitude: (json['longitude'] as num?)?.toDouble() ?? 0.0,
      timeZone: json['time_zone']?.toString() ?? '',
      country: json['country']?.toString() ?? '',
      city: json['city']?.toString() ?? '',
      continent: json['continent']?.toString() ?? '',
      accuracyRadius: (json['accuracy_radius'] as num?)?.toDouble() ?? 0.0,
      region: json['region']?.toString() ?? '',
    );
  }
  final double latitude;
  final double longitude;
  final String timeZone;
  final String country;
  final String city;
  final String continent;
  final double accuracyRadius;
  final String region;

  Map<String, dynamic> toJson() => {
        'latitude': latitude,
        'longitude': longitude,
        'time_zone': timeZone,
        'country': country,
        'city': city,
        'continent': continent,
        'accuracy_radius': accuracyRadius,
        'region': region,
      };
}

class GpuInfo {
  const GpuInfo({
    this.vendor = '',
    this.subVendor = '',
    this.model = '',
    this.bus = '',
    this.busAddress = '',
    this.vram = 0.0,
    this.vramDynamic = false,
    this.pciID = '',
  });

  factory GpuInfo.fromJson(Map<String, dynamic>? json) {
    if (json == null) return const GpuInfo();

    return GpuInfo(
      vendor: json['vendor']?.toString() ?? '',
      subVendor: json['subVendor']?.toString() ?? '',
      model: json['model']?.toString() ?? '',
      bus: json['bus']?.toString() ?? '',
      busAddress: json['busAddress']?.toString() ?? '',
      vram: (json['vram'] as num?)?.toDouble() ?? 0.0,
      vramDynamic: json['vramDynamic'] as bool? ?? false,
      pciID: json['pciID']?.toString() ?? '',
    );
  }
  final String vendor;
  final String subVendor;
  final String model;
  final String bus;
  final String busAddress;
  final double vram;
  final bool vramDynamic;
  final String pciID;

  Map<String, dynamic> toJson() => {
        'vendor': vendor,
        'subVendor': subVendor,
        'model': model,
        'bus': bus,
        'busAddress': busAddress,
        'vram': vram,
        'vramDynamic': vramDynamic,
        'pciID': pciID,
      };
}

class UtilizationPercentages {
  const UtilizationPercentages({
    this.cpuUtilizationPercentile = 0.0,
    this.ramUtilizationPercentile = 0.0,
    this.diskReadPercentile = 0.0,
    this.diskWritePercentile = 0.0,
    this.networkInboundPercentile = 0.0,
    this.networkOutboundPercentile = 0.0,
  });

  factory UtilizationPercentages.fromJson(Map<String, dynamic>? json) {
    if (json == null) return const UtilizationPercentages();

    return UtilizationPercentages(
      cpuUtilizationPercentile:
          (json['cpuUtilizationPercentile'] as num?)?.toDouble() ?? 0.0,
      ramUtilizationPercentile:
          (json['ramUtilizationPercentile'] as num?)?.toDouble() ?? 0.0,
      diskReadPercentile:
          (json['diskReadPercentile'] as num?)?.toDouble() ?? 0.0,
      diskWritePercentile:
          (json['diskWritePercentile'] as num?)?.toDouble() ?? 0.0,
      networkInboundPercentile:
          (json['networkInboundPercentile'] as num?)?.toDouble() ?? 0.0,
      networkOutboundPercentile:
          (json['networkOutboundPercentile'] as num?)?.toDouble() ?? 0.0,
    );
  }
  final double cpuUtilizationPercentile;
  final double ramUtilizationPercentile;
  final double diskReadPercentile;
  final double diskWritePercentile;
  final double networkInboundPercentile;
  final double networkOutboundPercentile;

  Map<String, dynamic> toJson() => {
        'cpuUtilizationPercentile': cpuUtilizationPercentile,
        'ramUtilizationPercentile': ramUtilizationPercentile,
        'diskReadPercentile': diskReadPercentile,
        'diskWritePercentile': diskWritePercentile,
        'networkInboundPercentile': networkInboundPercentile,
        'networkOutboundPercentile': networkOutboundPercentile,
      };
}

// Previous LocationInfo, GpuInfo, and UtilizationPercentages classes remain unchanged
