// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:nexqloud/core/utils/utils.dart';

class ServerInfo {
  ServerInfo({
    required this.hostName,
    required this.latitude,
    required this.longitude,
    required this.region,
    this.serverId = '',
    this.licenseId = '',
    this.nftId = 0,
  });

  factory ServerInfo.fromJson(Map<String, dynamic> json) {
    // Parse the region first.
    final region = _parseString(json['region']) ?? '';

    // Attempt to parse latitude and longitude from JSON.
    final latFromJson = _parseDouble(json['latitude']);
    final lngFromJson = _parseDouble(json['longitude']);

    return ServerInfo(
      hostName: _parseString(json['hostName']) ?? '',
      // If region is provided and latitude is missing, assign default lat.
      latitude: (region.isNotEmpty && latFromJson == null)
          ? getDefaultRegionLat(region)
          : (latFromJson ?? 0.0),
      // If region is provided and longitude is missing, assign default lng.
      longitude: (region.isNotEmpty && lngFromJson == null)
          ? getDefaultRegionLng(region)
          : (lngFromJson ?? 0.0),
      region: region,
      serverId: _parseString(json['serverId']) ?? '',
      licenseId: _parseString(json['licenseId']) ?? '',
      nftId: _parseInt(json['nftId']) ?? 0,
    );
  }
  final String hostName;
  final double latitude;
  final double longitude;
  final String region;
  final String serverId;
  final String licenseId;
  final int nftId;

  String get licenseKey => nftId.toString().padLeft(6, '0');

  //CopyWith
  ServerInfo copyWith({
    String? hostName,
    double? latitude,
    double? longitude,
    String? region,
    String? serverId,
    String? licenseId,
    int? nftId,
  }) {
    return ServerInfo(
      hostName: hostName ?? this.hostName,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      region: region ?? this.region,
      serverId: serverId ?? this.serverId,
      licenseId: licenseId ?? this.licenseId,
      nftId: nftId ?? this.nftId,
    );
  }

  // handle null
  static double? _parseDouble(dynamic value) {
    if (value == null || value.toString().isEmpty) return null;
    if (value is num) return value.toDouble();
    try {
      return double.parse(value.toString());
    } catch (e) {
      return null;
    }
  }

  static int? _parseInt(dynamic value) {
    if (value == null || value.toString().isEmpty) return null;
    if (value is num) return value.toInt();
    try {
      return int.parse(value.toString());
    } catch (e) {
      return null;
    }
  }

  static String? _parseString(dynamic value) {
    if (value == null || value.toString().isEmpty) return null;
    return value.toString();
  }

  @override
  String toString() {
    return 'ServerInfo(hostName: $hostName, latitude: $latitude, longitude: $longitude, region: $region, serverId: $serverId, licenseId: $licenseId, nftId: $nftId)';
  }

  bool get dontHaveLatLng => latitude == 0 && longitude == 0;
}
