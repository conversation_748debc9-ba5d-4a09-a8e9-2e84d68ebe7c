class ServerProvider {
  ServerProvider({
    required this.id,
    required this.providerId,
    required this.cpu,
    required this.createdAt,
    required this.gpu,
    required this.gpuModels,
    required this.ipAddress,
    required this.macAddress,
    required this.macAddresses,
    required this.memory,
    required this.serviceUrl,
    required this.storage,
    required this.updatedAt,
  });

  factory ServerProvider.fromJson(Map<String, dynamic> json) => ServerProvider(
        id: json['_id']?.toString() ?? '',
        providerId: json['providerId']?.toString() ?? '',
        cpu: json['cpu'] as double? ?? 0.0,
        createdAt: json['createdAt'] != null
            ? DateTime.tryParse(json['createdAt'].toString()) ?? DateTime.now()
            : DateTime.now(),
        gpu: json['gpu'] as double? ?? 0.0,
        gpuModels: (json['gpuModels'] as List<dynamic>?)
                ?.map((e) => e?.toString() ?? '')
                .toList() ??
            [],
        ipAddress: json['ipAddress']?.toString() ?? '',
        macAddress: json['macAddress']?.toString() ?? '',
        macAddresses: (json['macAddresses'] as List<dynamic>?)
                ?.map((e) => e?.toString())
                .toList() ??
            [],
        memory: json['memory'] as double? ?? 0.0,
        serviceUrl: json['serviceUrl']?.toString() ?? '',
        storage: (json['storage'] as num?)?.toDouble() ?? 0.0,
        updatedAt: json['updatedAt'] != null
            ? DateTime.tryParse(json['updatedAt'].toString()) ?? DateTime.now()
            : DateTime.now(),
      );
  final String id;
  final String providerId;
  final double cpu;
  final DateTime createdAt;
  final double gpu;
  final List<String> gpuModels;
  final String ipAddress;
  final String macAddress;
  final List<String?> macAddresses;
  final double memory;
  final String serviceUrl;
  final double storage;
  final DateTime updatedAt;

  Map<String, dynamic> toJson() => {
        '_id': id,
        'providerId': providerId,
        'cpu': cpu,
        'createdAt': createdAt.toIso8601String(),
        'gpu': gpu,
        'gpuModels': gpuModels,
        'ipAddress': ipAddress,
        'macAddress': macAddress,
        'macAddresses': macAddresses,
        'memory': memory,
        'serviceUrl': serviceUrl,
        'storage': storage,
        'updatedAt': updatedAt.toIso8601String(),
      };
}
