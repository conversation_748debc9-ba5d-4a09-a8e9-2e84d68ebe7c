class DeviceStats {
  DeviceStats({
    required this.totalDevices,
    required this.devicesOnline,
    required this.totalGPUs,
    required this.totalThreads,
    required this.totalDiskSpace,
    required this.totalBandwidth,
    required this.networkInbound,
    required this.networkOutbound,
    required this.cpuUtilization,
    required this.ramUtilization,
    required this.diskIO,
    required this.diskUsage,
    required this.totalPacketLoss,
    required this.uptimePercentage,
    required this.offlineEvents,
  });

  factory DeviceStats.empty() => DeviceStats(
        totalDevices: 0,
        devicesOnline: 0,
        totalGPUs: 0,
        totalThreads: 0,
        totalDiskSpace: 0,
        totalBandwidth: 0,
        networkInbound: 0,
        networkOutbound: 0,
        cpuUtilization: 0,
        ramUtilization: 0,
        diskIO: 0,
        diskUsage: 0,
        totalPacketLoss: 0,
        uptimePercentage: 0,
        offlineEvents: '',
      );

  // Factory method to create a DeviceStats instance from a JSON object
  factory DeviceStats.fromJson(Map<String, dynamic> json) {
    return DeviceStats(
      totalDevices: json['totalDevices'],
      devicesOnline: json['devicesOnline'],
      totalGPUs: json['totalGPUs'],
      totalThreads: json['totalThreads'],
      totalDiskSpace: json['totalDiskSpace'],
      totalBandwidth: double.tryParse(json['totalBandwidth'].toString()) ?? 0.0,
      networkInbound: double.tryParse(json['networkInbound'].toString()) ?? 0.0,
      networkOutbound:
          double.tryParse(json['networkOutbound'].toString()) ?? 0.0,
      cpuUtilization: double.tryParse(json['cpuUtilization'].toString()) ?? 0.0,
      ramUtilization: double.tryParse(json['ramUtilization'].toString()) ?? 0.0,
      diskIO: double.tryParse(json['diskIO'].toString()) ?? 0.0,
      diskUsage: double.tryParse(json['diskUsage'].toString()) ?? 0.0,
      totalPacketLoss:
          double.tryParse(json['totalPacketLoss'].toString()) ?? 0.0,
      uptimePercentage:
          double.tryParse(json['uptimePercentage'].toString()) ?? 0.0,
      offlineEvents: json['offlineEvents'] ?? '',
    );
  }
  final int totalDevices;
  final int devicesOnline;
  final int totalGPUs;
  final int totalThreads;
  final int totalDiskSpace;
  final double totalBandwidth;
  final double networkInbound;
  final double networkOutbound;
  final double cpuUtilization;
  final double ramUtilization;
  final double diskIO;
  final double diskUsage;
  final double totalPacketLoss;
  final double uptimePercentage;
  final String offlineEvents;

  // Method to convert DeviceStats to JSON
  Map<String, dynamic> toJson() {
    return {
      'totalDevices': totalDevices,
      'devicesOnline': devicesOnline,
      'totalGPUs': totalGPUs,
      'totalThreads': totalThreads,
      'totalDiskSpace': totalDiskSpace,
      'totalBandwidth': totalBandwidth,
      'networkInbound': networkInbound,
      'networkOutbound': networkOutbound,
      'cpuUtilization': cpuUtilization,
      'ramUtilization': ramUtilization,
      'diskIO': diskIO,
      'diskUsage': diskUsage,
      'totalPacketLoss': totalPacketLoss,
      'uptimePercentage': uptimePercentage,
      'offlineEvents': offlineEvents,
    };
  }
}
