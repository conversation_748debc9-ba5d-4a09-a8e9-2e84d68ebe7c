class GpuInfo {
  GpuInfo({
    required this.vendor,
    required this.subVendor,
    required this.model,
    required this.bus,
    required this.busAddress,
    required this.vram,
    required this.vramDynamic,
    required this.pciID,
  });

  factory GpuInfo.fromJson(Map<String, dynamic> json) => GpuInfo(
        vendor: json['vendor']?.toString() ?? '',
        subVendor: json['subVendor']?.toString() ?? '',
        model: json['model']?.toString() ?? '',
        bus: json['bus']?.toString() ?? '',
        busAddress: json['busAddress']?.toString() ?? '',
        vram: json['vram'] as double? ?? 0.0,
        vramDynamic: json['vramDynamic'] as bool? ?? false,
        pciID: json['pciID']?.toString() ?? '',
      );
  final String vendor;
  final String subVendor;
  final String model;
  final String bus;
  final String busAddress;
  final double vram;
  final bool vramDynamic;
  final String pciID;

  Map<String, dynamic> toJson() => {
        'vendor': vendor,
        'subVendor': subVendor,
        'model': model,
        'bus': bus,
        'busAddress': busAddress,
        'vram': vram,
        'vramDynamic': vramDynamic,
        'pciID': pciID,
      };
}
