import 'dart:math';

import 'package:animate_do/animate_do.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/double.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/ui/widgets/blurred_background.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/features/main/models/server_info.dart';
import 'package:nexqloud/features/main/providers/server_data_provider.dart';
import 'package:nexqloud/features/main/ui/widgets/complete_info_container.dart';
import 'package:nexqloud/features/main/ui/widgets/selected_marker_sheet_view.dart';
import 'package:nexqloud/features/main/ui/widgets/vertical_zoom_slider.dart';
import 'package:nexqloud/generated/assets.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_maps/maps.dart';

class ExpandedMobileMapView extends StatefulWidget {
  const ExpandedMobileMapView({
    super.key,
    required this.worldMapZoomLevel,
    required this.initialMarkersCount,
    required this.onCollapsed,
    required this.toggleModal,
    required this.isModalVisible,
    this.selectedMarker,
  });
  final double worldMapZoomLevel;
  final int initialMarkersCount;
  final VoidCallback onCollapsed;
  final ValueChanged<bool> toggleModal;
  final bool isModalVisible;
  final ServerInfo? selectedMarker;

  @override
  State<ExpandedMobileMapView> createState() => _ExpandedMobileMapViewState();
}

class _ExpandedMobileMapViewState extends State<ExpandedMobileMapView>
    with SingleTickerProviderStateMixin {
  final double minLat = 34.0522; // Los Angeles (South)
  final double maxLat = 37.7749; // San Francisco (North)
  final double minLng = -122.4194; // West
  final double maxLng = -118.2437; // East

  late MapZoomPanBehavior _worldMapZoomPanBehavior;
  late MapTileLayerController _worldMapTileLayerController;

  double _worldMapZoomLevel = 5;
  List<int> _markerIndices = [];
  ServerInfo? _selectedMarker;
  bool _isModalDisplayed = false;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _worldMapTileLayerController = MapTileLayerController();

    _setZoomPanBehavior();
    _updateMarkerIndices();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.selectedMarker != null) {
        _onMapMarkerTapped(widget.selectedMarker!);
      }
    });

    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..repeat(reverse: true);

    // Define the animation scaling factor
    _pulseAnimation = Tween<double>(begin: 1, end: 1.3).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  void _setZoomPanBehavior() {
    _worldMapZoomPanBehavior = MapZoomPanBehavior(
      enableDoubleTapZooming: true,
      zoomLevel: 2,
      showToolbar: false,
      // latLngBounds: const MapLatLngBounds(
      //   MapLatLng(
      //     40.7128,
      //     -74.0060,
      //   ),
      //   MapLatLng(
      //     42.7128,
      //     -72.0060,
      //   ),
      // ),
      toolbarSettings: const MapToolbarSettings(
        itemBackgroundColor: graphlinecolor2,
        iconColor: kWhite,
        itemHoverColor: kPurpleColor,
        direction: Axis.vertical,
        position: MapToolbarPosition.bottomRight,
      ),
    );
  }

  void _updateMarkerIndices() {
    final dataLength =
        context.read<ServerDataProvider>().serversWithLatLng.length;
    _markerIndices = List<int>.generate(dataLength, (i) => i);
    if (_selectedMarker != null) {
      final selectedIndex = context
          .read<ServerDataProvider>()
          .serversWithLatLng
          .indexOf(_selectedMarker!);
      _markerIndices.remove(selectedIndex);
      _markerIndices.add(selectedIndex); // Move selected index to end
    }
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ServerDataProvider>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.isMobile || context.isTablet) {
        if (_selectedMarker != null && !_isModalDisplayed) {
          _isModalDisplayed = true; // Prevent repeated modal displays
          _showMobileModal(context);
        }
      } else {
        _isModalDisplayed = false;
        // dismiss the modal
        Navigator.maybePop(context);
      }
    });
    if (context.isDesktop || context.isDesktopLarge) {
      provider.toggleMapExpand(
        false,
      );
    }

    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: SfMapsTheme(
            data: SfMapsThemeData(
              shapeHoverColor: kTransparent,
              shapeHoverStrokeColor: kWhite.withOpacity(0.22),
              tooltipColor: kWhite.withOpacity(0.2),
              tooltipStrokeColor: kWhite.withOpacity(0.1),
              tooltipBorderRadius: BorderRadius.circular(15),
            ),
            child: ColoredBox(
              color: widget.worldMapZoomLevel >= 5
                  ? const Color(0xFFD4DADC)
                  : kTransparent,
              child: SfMaps(
                layers: <MapLayer>[
                  MapTileLayer(
                    urlTemplate:
                        'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png',
                    initialMarkersCount: widget.initialMarkersCount,
                    initialFocalLatLng: _worldMapZoomPanBehavior.focalLatLng ??
                        const MapLatLng(
                          0,
                          0,
                        ),
                    controller: _worldMapTileLayerController,
                    initialZoomLevel: widget.worldMapZoomLevel.toInt(),
                    zoomPanBehavior: _worldMapZoomPanBehavior,
                    markerBuilder: _buildMapMarker,
                    // WillPan callback to prevent panning outside the bounding box
                    // onWillPan: (details) {
                    //   // The new lat/long that the map is about to center on.
                    //   final newFocalLatLng = details.focalLatLng;
                    //   if (newFocalLatLng == null) return false;
                    //
                    //   // If we’re about to pan outside the bounding box, return false to block it.
                    //   if (newFocalLatLng.latitude < minLat ||
                    //       newFocalLatLng.latitude > maxLat ||
                    //       newFocalLatLng.longitude < minLng ||
                    //       newFocalLatLng.longitude > maxLng) {
                    //     return false;
                    //   }
                    //   return true;
                    // },
                  ),
                ],
              ),
            ),
          ),
        ),
        if (_selectedMarker == null)
          Positioned(
            top: 10,
            right: 13,
            child: GestureDetector(
              onTap: () {
                context.read<ServerDataProvider>().toggleMapExpand(false);
                widget.onCollapsed();

                widget.toggleModal(!widget.isModalVisible);
              },
              child: BlurredBackground(
                borderRadius: 8,
                child: Container(
                  height: context.isTablet ? 42 : 32,
                  width: context.isTablet ? 42 : 32,
                  decoration: widget.worldMapZoomLevel >= 5
                      ? ShapeDecoration(
                          color: Colors.black.withOpacity(0.0001),
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                              width: 0.50,
                              color:
                                  Colors.black.withOpacity(0.10000000149011612),
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        )
                      : BoxDecoration(
                          color: widget.worldMapZoomLevel < 5
                              ? kBorderColor.withOpacity(0.12)
                              : graphlinecolor2,
                          borderRadius: BorderRadius.circular(8),
                          gradient: widget.worldMapZoomLevel >= 5
                              ? const LinearGradient(
                                  colors: [
                                    gradientColorOne,
                                    gradientColorTwo,
                                  ],
                                )
                              : null,
                        ),
                  child: SvgPicture.asset(
                    Assets.svgCollapseMap,
                    height: 40,
                    width: 40,
                    color: widget.worldMapZoomLevel >= 5 ? kBlack : null,
                  ),
                ),
              ),
            ),
          ),
        Positioned(
          left: 24,
          top: context.isTablet ? 28 : 16,
          right: 20,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'NETWORK MAP',
                style: TextStyle(
                  color: Color(0xFF1A1143),
                  fontSize: 14,
                  fontFamily: 'Rubik',
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Space.vertical(13),
              Container(
                alignment: context.isMobileOrientationWide
                    ? null
                    : Alignment.centerLeft,
                child: context.isLandscape
                    ? Builder(
                        builder: (context) {
                          final data = provider.systemMetrics;
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            spacing: 8,
                            children: [
                              CompleteInfoContainer(
                                title: data.devicesOnline.toString(),
                                subtitle: 'Devices Online',
                              ),
                              CompleteInfoContainer(
                                title: data.totalVCPUCores.toK,
                                subtitle: 'Total vCPU',
                              ),
                              CompleteInfoContainer(
                                title:
                                    '${data.ramUtilization.toStringAsFixed(2)}%',
                                subtitle: 'RAM Utilization',
                              ),
                              CompleteInfoContainer(
                                title:
                                    '${data.cpuUtilization.toStringAsFixed(2)}%',
                                subtitle: 'CPU Utilization',
                              ),
                            ],
                          );
                        },
                      )
                    : Builder(
                        builder: (context) {
                          final data = provider.systemMetrics;
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Expanded(
                                child: CompleteInfoContainer(
                                  title: data.devicesOnline.toString(),
                                  subtitle: 'Devices Online',
                                ),
                              ),
                              const Space.horizontal(8),
                              Expanded(
                                child: CompleteInfoContainer(
                                  title: data.totalVCPUCores.toK,
                                  subtitle: 'Total vCPU',
                                ),
                              ),
                              const Space.horizontal(8),
                              Expanded(
                                child: CompleteInfoContainer(
                                  title:
                                      '${data.ramUtilization.toStringAsFixed(2)}%',
                                  subtitle: 'RAM Utilization',
                                ),
                              ),
                              const Space.horizontal(8),
                              Expanded(
                                child: CompleteInfoContainer(
                                  title:
                                      '${data.cpuUtilization.toStringAsFixed(2)}%',
                                  subtitle: 'CPU Utilization',
                                ),
                              ),
                            ],
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
        Positioned(
          bottom: 60,
          right: 15,
          child: VerticalZoomSlider(
            zoomValue: _worldMapZoomLevel,
            maxZoom: 15,
            onChanged: _setZoomLevel,
            isExpandedView: true,
          ),
        ),
      ],
    );
  }

  void _showMobileModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      enableDrag: false,
      barrierColor: Colors.transparent,
      constraints: BoxConstraints(
        maxWidth: context.isLandscape ? context.width * 0.6 : context.width,
      ),
      builder: (context) {
        return SelectedMarkerSheetView(
          selectedMarker: _selectedMarker,
        );
      },
    ).then((value) {
      _selectedMarker = null;
      _isModalDisplayed = false;
      setState(() {});
      _updateWhenMarkerUnselected();
    });
  }

  // update when marker unselected
  void _updateWhenMarkerUnselected() {
    _worldMapTileLayerController.updateMarkers(
      List<int>.generate(_markerIndices.length, (i) => i),
    );
  }

  void _setZoomLevel(double value) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final newZoom = value.clamp(2.0, 15.0);
      _worldMapZoomPanBehavior.zoomLevel = newZoom;
      _worldMapZoomLevel = newZoom;
      // _worldMapZoomPanBehavior.latLngBounds = const MapLatLngBounds(
      //   MapLatLng(
      //     40.7128,
      //     -74.0060,
      //   ),
      //   MapLatLng(
      //     42.7128,
      //     -72.0060,
      //   ),
      // );

      // if (value < 5) _selectedMarker = null;

      setState(() {});
    });
  }

  MapMarker _buildMapMarker(BuildContext context, int index) {
    final dataIndex = _markerIndices[index];

    double markerSize;

    markerSize = 10.0;

    final marker =
        context.read<ServerDataProvider>().serversWithLatLng[dataIndex];

    return MapMarker(
      latitude: marker.latitude,
      longitude: marker.longitude,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 40,
            width: 120,
            margin: const EdgeInsets.only(bottom: 10),
            child: _selectedMarker != null && _selectedMarker == marker
                ? TooltipBubble(text: _selectedMarker!.licenseKey)
                : const SizedBox(),
          ),
          ZoomIn(
            child: GestureDetector(
              onTap: () => _onMapMarkerTapped(marker),
              child: Builder(
                builder: (context) {
                  final isSelected = _selectedMarker == marker;

                  return AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: isSelected ? _pulseAnimation.value : 1.0,
                        child: Container(
                          height: markerSize,
                          width: markerSize,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: isSelected
                                ? [
                                    const BoxShadow(
                                      blurRadius: 15,
                                      spreadRadius: 2,
                                      color: Color(0xFF935BDA),
                                    ),
                                  ]
                                : null,
                            color: isSelected
                                ? const Color(0xFF935BDA)
                                : graphlinecolor2.withOpacity(0.8),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onMapMarkerTapped(ServerInfo marker) {
    // Update previous selected marker
    if (_selectedMarker != null) {
      final previousDataIndex = context
          .read<ServerDataProvider>()
          .serversWithLatLng
          .indexOf(_selectedMarker!);
      final previousMarkerIndex = _markerIndices.indexOf(previousDataIndex);

      _worldMapTileLayerController.updateMarkers([previousMarkerIndex]);
    }

    _selectedMarker = marker;
    _updateMarkerIndices(); // Update marker indices since selected marker changed

    _worldMapTileLayerController
        .updateMarkers(List<int>.generate(_markerIndices.length, (i) => i));

    // Set the zoom level directly to 15 on marker tap
    _worldMapZoomPanBehavior.zoomLevel = 15;
    _worldMapZoomLevel = 15;

    // Update focalLatLng to center on the tapped marker
    final lat = _selectedMarker!.latitude;
    final lon = _selectedMarker!.longitude;

    // With a zoom level of 15, calculate the new focal point
    final pixelOffsetY = context.isLandscape
        ? -115
        : context.height < 600
            ? -120
            : context.height > 600 && context.height < 700
                ? -170
                : context.height > 800
                    ? -100
                    : -165;
    double sinhCustom(double x) => (exp(x) - exp(-x)) / 2;

    final adjustedZoom = _worldMapZoomPanBehavior.zoomLevel;
    final adjustedN = pow(2.0, adjustedZoom);
    final latRad = lat * pi / 180.0;
    final adjustedXTile = (lon + 180.0) / 360.0 * adjustedN;
    final adjustedYTile =
        (1.0 - log(tan(latRad) + 1 / cos(latRad)) / pi) / 2.0 * adjustedN;

    final adjPixelX = adjustedXTile * 256.0;
    var adjPixelY = adjustedYTile * 256.0;

    adjPixelY -= pixelOffsetY; // Apply the offset

    final adjNewXTile = adjPixelX / 256.0;
    final adjNewYTile = adjPixelY / 256.0;

    final adjNewLon = adjNewXTile / adjustedN * 360.0 - 180.0;
    final adjNewLatRad =
        atan(sinhCustom(pi * (1 - 2 * adjNewYTile / adjustedN)));
    final adjNewLat = adjNewLatRad * 180.0 / pi;

    _worldMapZoomPanBehavior.focalLatLng = MapLatLng(adjNewLat, adjNewLon);

    setState(() {});
  }
}

class TooltipBubble extends StatelessWidget {
  const TooltipBubble({super.key, required this.text});
  final String text;

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: const Size(76, 26), // Adjust the size as needed
      painter: TooltipBubblePainter(),
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
        ),
        child: Padding(
          padding: const EdgeInsets.only(
            bottom: 8,
          ),
          child: Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

class TooltipBubblePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    const borderRadius = 8.0;
    const pointerHeight = 10.0;
    const pointerWidth = 20.0;

    final path = Path();

    // Draw the rounded rectangle
    path.moveTo(borderRadius, 0);
    path.lineTo(size.width - borderRadius, 0);
    path.quadraticBezierTo(
      size.width,
      0,
      size.width,
      borderRadius,
    ); // Top-right corner
    path.lineTo(size.width, size.height - borderRadius - pointerHeight);
    path.quadraticBezierTo(
      size.width,
      size.height - pointerHeight,
      size.width - borderRadius,
      size.height - pointerHeight,
    ); // Bottom-right corner
    path.lineTo(size.width / 2 + pointerWidth / 2, size.height - pointerHeight);

    // Draw the triangular pointer
    path.lineTo(size.width / 2, size.height);
    path.lineTo(size.width / 2 - pointerWidth / 2, size.height - pointerHeight);

    // Continue the rounded rectangle
    path.lineTo(borderRadius, size.height - pointerHeight);
    path.quadraticBezierTo(
      0,
      size.height - pointerHeight,
      0,
      size.height - borderRadius - pointerHeight,
    ); // Bottom-left corner
    path.lineTo(0, borderRadius);
    path.quadraticBezierTo(0, 0, borderRadius, 0); // Top-left corner

    // Apply gradient
    const gradient = LinearGradient(
      colors: [Color(0xFF4B31BD), Color(0xFF25314C)],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    );

    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final paint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, paint);

    // Add border
    final borderPaint = Paint()
      ..color = const Color(0xFF836FFF)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
