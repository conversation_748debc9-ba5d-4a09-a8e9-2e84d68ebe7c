import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/double.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/navigation/router.dart';
import 'package:nexqloud/core/ui/widgets/custom_gradient_button.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/core/utils/utils.dart';
import 'package:nexqloud/features/main/models/server_info.dart';
import 'package:nexqloud/features/main/models/server_model.dart';
import 'package:nexqloud/features/main/providers/server_data_provider.dart';
import 'package:nexqloud/features/main/ui/widgets/blurred_dropdown_widget.dart';
import 'package:nexqloud/features/main/ui/widgets/online_server_indicator.dart';
import 'package:nexqloud/generated/assets.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class TransparentDataGrid extends StatefulWidget {
  const TransparentDataGrid({
    super.key,
    required this.onCellTapped,
  });
  final VoidCallback onCellTapped;

  @override
  TransparentDataGridState createState() => TransparentDataGridState();
}

class TransparentDataGridState extends State<TransparentDataGrid> {
  List<String> locations = [
    'us-east-1',
    'us-east-2',
    'us-west-1',
    'us-west-2',
    'ca-central-1',
    'eu-north-1',
    'eu-south-1',
    'eu-west-1',
    'eu-west-2',
    'eu-west-3',
    'eu-central-1',
    'sa-east-1',
    'ap-southeast-1',
    'ap-southeast-2',
    'ap-northeast-1',
    'ap-northeast-2',
    'ap-northeast-3',
    'ap-south-1',
    'ap-east-1',
    'me-south-1',
    'af-south-1',
  ];
  List<String> regions = [
    'All',
  ];
  List<ProviderModel> originalProviderData = [];
  List<ProviderModel> _filteredData = [];

  late List<ProviderModel> providerData;
  late ProviderDataSource _providerDataSource;
  String _selectedRegion = 'All';
  String _selectedStatus = 'All';
  int pageIndex = 0;
  String searchQuery = '';
  final int rowsPerPage = 10;

  bool get isOnline => _selectedStatus == 'All';

  late TextEditingController _searchController;
  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    regions = [...regions, ...locations.toSet()];
    _onInit();
  }

  void _onInit() {
    _fetchAndGenerateProviderData();
    _filter();
    pageIndex = context.read<ServerDataProvider>().pageNumber;
  }

  @override
  void dispose() {
    super.dispose();
    _searchController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ServerDataProvider>();
    if (!provider.isTableDateLoading) {
      _onInit();
    }

    return SizedBox(
      width: (context.isDesktop || context.isDesktopLarge)
          ? context.horizontalPadding
          : context.horizontalPadding,
      child: Column(
        children: [
          if (context.isMobile || context.isTablet) const Space.vertical(20),
          _buildSearchBar(),
          SizedBox(
            height: 548,
            child: SfDataGridTheme(
              data: SfDataGridThemeData(
                headerColor: gradientColorTwo.withOpacity(0.99),
                headerHoverColor: gradientColorTwo.withOpacity(0.8),
                rowHoverColor: gradientColorTwo.withOpacity(0.3),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: ColoredBox(
                  color: kWhite.withOpacity(0.04),
                  child: provider.isTableDateLoading
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : _filteredData.isEmpty
                          ? const Center(
                              child: Text(
                                'No devices found',
                                style: TextStyle(
                                  color: kWhite,
                                  fontSize: 18,
                                ),
                              ),
                            )
                          : SfDataGrid(
                              source: _providerDataSource,
                              rowsPerPage: rowsPerPage,
                              verticalScrollPhysics:
                                  const NeverScrollableScrollPhysics(),
                              columnWidthMode: context.isMobile
                                  ? ColumnWidthMode.auto
                                  : ColumnWidthMode.fill,
                              onCellTap: (details) {
                                if (details.rowColumnIndex.rowIndex > 0) {
                                  context
                                      .read<ServerDataProvider>()
                                      .serverModel = null;
                                  final provider =
                                      context.read<ServerDataProvider>();
                                  final index =
                                      details.rowColumnIndex.rowIndex - 1;
                                  // Row index: $index

                                  final data = providerData[index];

                                  final tableData = provider.tableData;

                                  final serverModel = tableData.firstWhere(
                                    (e) => e.serverId == data.serverId,
                                    orElse: () => ServerModel.empty(),
                                  );
                                  provider.serverModel = ServerInfo(
                                    hostName: serverModel.serverId,
                                    latitude: serverModel.latitude,
                                    longitude: serverModel.longitude,
                                    region: serverModel.region,
                                  );
                                  // Server model updated: ${provider.serverModel}
                                  if (serverModel.serverId.isEmpty) {
                                    return;
                                  }

                                  provider.isRowSelected = true;

                                  provider.toggleMapExpand(true);

                                  provider.fetchSingleServerDataViaServerId(
                                    serverModel.serverId,
                                  );
                                  widget.onCellTapped();
                                }
                              },
                              columns: <GridColumn>[
                                _buildGridColumn('NFT Key', 'name'),
                                _buildGridColumn('Region', 'region'),
                                _buildGridColumn('Model', 'model'),
                                _buildGridColumn('CPU', 'cpu'),
                                _buildGridColumn('Qloud Score', 'qloudScore'),
                                _buildGridColumn('Memory', 'memory'),
                                _buildGridColumn('Status', 'audited'),
                                _buildGridColumn('Uptime', 'uptime'),
                              ],
                            ),
                ),
              ),
            ),
          ),
          const Space.vertical(20),
          Divider(
            color: kWhite.withOpacity(0.4),
          ),
          const Space.vertical(10),
          _buildPagination(),
        ],
      ),
    );
  }

  Future<List<ProviderModel>> _fetchAndGenerateProviderData() async {
    _providerDataSource = ProviderDataSource(providerData: []);

    final providers = <ProviderModel>[];

    final rows = context.read<ServerDataProvider>().tableData;

    for (var i = 0; i < rows.length; i++) {
      final row = rows[i];

      //To remove any device which do not include any NFT Key
      if (row.nftTokenId.isEmpty || row.nftTokenId == '000000') {
        continue;
      }

      final provider = ProviderModel(
        name: '${row.nftTokenId} ${row.nftImage}' ?? '',
        model: row.model,
        online: row.isOnline,
        uptime: row.uptime,
        cpu: '${row.cpu.toStringAsFixed(2)}%',
        memory: convertToGBFromKB(row.memoryTotal),
        disk: row.diskUsage.toStringAsFixed(2),
        qloudscore: row.cloudScore.toPercentage ?? '',
        location: row.region != '' ? row.region : 'Unknown',
        gpu: '',
        audited: '',
        price: '',
        licenseImage: row.nftImage,
        serverId: row.serverId,
      );

      providers.add(provider);
    }

    originalProviderData = providers;

    _filteredData = List<ProviderModel>.from(originalProviderData);

    providerData = _getPageData(_filteredData, pageIndex, rowsPerPage);
    _providerDataSource = ProviderDataSource(providerData: providerData);

    setState(() {});

    return providers;
  }

  String convertToGBFromKB(double bytes) {
    final gb = bytes / (1024 * 1024 * 1024);
    return gb.toStringAsFixed(0);
  }

  void _filter() {
    _filteredData = List<ProviderModel>.from(originalProviderData);

    if (_selectedRegion.isNotEmpty) {
      if (_selectedRegion != 'All') {}
    }

    if (_selectedStatus.isNotEmpty) {
      if (_selectedStatus != 'All') {
        _filteredData = _filteredData
            .where(
              (e) => e.online == (_selectedStatus.toLowerCase() == 'online'),
            )
            .toList();
      }
    }

    if (searchQuery.isNotEmpty) {
      final queryLower = searchQuery.toLowerCase();

      _filteredData = _filteredData.where((element) {
        final nameLower = element.name.toLowerCase();
        final locationLower = element.location.toLowerCase();
        final modelLower = element.model.toLowerCase();
        final uptimeLower = element.uptime.toLowerCase();
        final cpuLower = element.cpu.toLowerCase();
        final memoryLower = '${element.memory} GB'.toLowerCase();
        final qloudscoreLower = element.qloudscore.toLowerCase();

        final regionName = regionMap[element.location] ?? '';
        final regionNameLower = regionName.toLowerCase();

        return nameLower.contains(queryLower) ||
            locationLower.contains(queryLower) ||
            modelLower.contains(queryLower) ||
            uptimeLower.contains(queryLower) ||
            cpuLower.contains(queryLower) ||
            memoryLower.contains(queryLower) ||
            qloudscoreLower.contains(queryLower) ||
            regionNameLower.contains(queryLower);
      }).toList();
    }

    pageIndex = 0;

    providerData = _getPageData(_filteredData, pageIndex, rowsPerPage);
    _providerDataSource = ProviderDataSource(providerData: providerData);
    setState(() {});
  }

  List<ProviderModel> _getPageData(
    List<ProviderModel> data,
    int pageIndex,
    int rowsPerPage,
  ) {
    if (pageIndex < 0) {
      // PageIndex cannot be negative, returning empty list
      return [];
    }

    final start = pageIndex * rowsPerPage;
    if (start >= data.length) {
      return const <ProviderModel>[];
    }
    final end = min(start + rowsPerPage, data.length);
    return data.sublist(start, end);
  }

  // void _onPageChange(int newPageIndex) {
  //   setState(() {
  //     pageIndex = newPageIndex;
  //     providerData = _getPageData(_filteredData, pageIndex, rowsPerPage);
  //     _providerDataSource = ProviderDataSource(providerData: providerData);
  //   });
  // }

  void _onSearch(String query) {
    setState(() {
      searchQuery = query.trim().toLowerCase();
    });
    _filter();
  }

  Widget _buildSearchBar() {
    return context.isDesktop
        ? _buildDesktopSearchBar()
        : _buildMobileSearchBar();
  }

  Widget _buildMobileSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: kWhite.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 15,
      ),
      margin: const EdgeInsets.only(
        bottom: 25,
      ),
      child: Column(
        children: [
          SizedBox(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Search',
                  style: context.normal!.copyWith(
                    color: kWhite,
                    fontSize: 16,
                  ),
                ),
                const Space.vertical(10),
                TextField(
                  onChanged: _onSearch,
                  decoration: InputDecoration(
                    hintText: 'Search Providers by NFT ID',
                    hintStyle: const TextStyle(color: kWhite),
                    prefixIcon: SvgPicture.asset(
                      'assets/icons/svg/search-lg.svg',
                    ),
                    border: GradientOutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      gradient: LinearGradient(
                        colors: [
                          kWhite.withOpacity(0.1),
                          gradientColorThree.withOpacity(0.1),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                  style: const TextStyle(color: kWhite),
                ),
              ],
            ),
          ),
          const Space.vertical(20),
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Rows',
                    style: context.normal!.copyWith(
                      color: kWhite,
                      fontSize: 16,
                    ),
                  ),
                  const Space.vertical(8),
                  Container(
                    height: 49,
                    width: context.width * 0.63 / 2,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color: kWhite.withOpacity(0.02),
                      borderRadius: BorderRadius.circular(8),
                      border: GradientBoxBorder(
                        gradient: LinearGradient(
                          colors: [
                            kWhite.withOpacity(0.1),
                            gradientColorThree.withOpacity(0.1),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                    ),
                    child: DropdownButton<String>(
                      value: _selectedRegion,
                      isExpanded: true,
                      underline: const SizedBox(),
                      icon: SvgPicture.asset(
                        'assets/icons/svg/drop_down_arrow_down.svg',
                        height: 18,
                        width: 18,
                      ),
                      dropdownColor: gradientColorTwo,
                      borderRadius: BorderRadius.circular(12),
                      menuMaxHeight: 300,
                      items: regions.map((value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(
                            value,
                            style: context.normal,
                          ),
                        );
                      }).toList(),
                      onChanged: (newValue) {
                        setState(() {
                          _selectedRegion = newValue!;
                        });
                      },
                    ),
                  ),
                ],
              ),
              const Space.horizontal(20),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filters:',
                    style: context.normal!.copyWith(
                      color: kWhite,
                      fontSize: 16,
                    ),
                  ),
                  const Space.vertical(8),
                  Container(
                    height: 49,
                    width: context.width * 0.63 / 2,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color: kWhite.withOpacity(0.02),
                      borderRadius: BorderRadius.circular(8),
                      border: GradientBoxBorder(
                        gradient: LinearGradient(
                          colors: [
                            kWhite.withOpacity(0.1),
                            gradientColorThree.withOpacity(0.1),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                    ),
                    child: DropdownButton<String>(
                      value: _selectedStatus,
                      isExpanded: true,
                      underline: const SizedBox(),
                      icon: SvgPicture.asset(
                        'assets/icons/svg/drop_down_arrow_down.svg',
                      ),
                      menuMaxHeight: 300,
                      dropdownColor: gradientColorTwo,
                      borderRadius: BorderRadius.circular(12),
                      items: ['Online', 'Offline'].map((value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(
                            value,
                            style: context.normal,
                          ),
                        );
                      }).toList(),
                      onChanged: (newValue) {
                        setState(() {
                          _selectedStatus = newValue!;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Container _buildDesktopSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: kWhite.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 25),
      margin: const EdgeInsets.only(
        bottom: 25,
        top: 25,
      ),
      child: Row(
        children: [
          Flexible(
            child: SizedBox(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: lightGray15per,
                      boxShadow: [
                        BoxShadow(
                          color: kBlack.withOpacity(0.03),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: DecoratedBox(
                      decoration: ShapeDecoration(
                        gradient: LinearGradient(
                          colors: [
                            kBlack.withOpacity(0.32),
                            kBlack.withOpacity(0.02),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          stops: const [0.0, 0.18],
                          tileMode: TileMode.decal,
                        ),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(14)),
                        ),
                      ),
                      child: TextField(
                        controller: _searchController,
                        onChanged: (value) {
                          if (value.isEmpty) {
                            _onPageChangeCall(
                              0,
                              refresh: true,
                            );
                          }
                        },
                        onSubmitted: (v) {
                          _onPageChangeCall(
                            0,
                            refresh: true,
                          );
                        },
                        cursorColor: kWhite,
                        decoration: InputDecoration(
                          hintText: 'Search Providers by NFT ID',
                          hintStyle: TextStyle(color: kWhite.withOpacity(0.5)),
                          prefixIcon: SizedBox(
                            height: 24,
                            width: 24,
                            child: Center(
                              child: SvgPicture.asset(
                                'assets/icons/svg/search_icon.svg',
                                height: 24,
                                width: 24,
                              ),
                            ),
                          ),
                          border: GradientOutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            gradient: LinearGradient(
                              colors: [
                                kWhite.withOpacity(0.15),
                                kWhite.withOpacity(0.15),
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                          ),
                        ),
                        style: const TextStyle(color: kWhite),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const Space.horizontal(20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              BlurredDropdownButton(
                isRegion: true,
                items: regions.map((value) {
                  return value;
                }).toList(),
                value: _selectedRegion,
                onChanged: (newValue) {
                  setState(() {
                    _selectedRegion = newValue!;
                  });
                  _onPageChangeCall(
                    0,
                    refresh: true,
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  GridColumn _buildGridColumn(
    String columnName,
    String fieldName,
  ) {
    // Context dimensions: w${context.width} - h${context.height} - s ${context.isDesktopSmall} - xs ${context.isDesktopExtraSmall}
    return GridColumn(
      columnName: fieldName,
      width: context.isMobile
          ? context.width * 0.22
          : columnName == 'NFT Key'
              ? context.isDesktopSmall
                  ? context.width * 0.09
                  : context.isDesktopExtraSmall
                      ? context.width * 0.1
                      : double.nan
              : double.nan,
      label: Container(
        padding: const EdgeInsets.all(8),
        margin: EdgeInsets.only(
          left: columnName == 'NFT Key'
              ? navigatorKey.currentState!.context.width * 0.016
              : columnName == 'Status'
                  ? navigatorKey.currentState!.context.width * 0.023
                  : columnName == 'Memory'
                      ? navigatorKey.currentState!.context.width * 0.02
                      : columnName == 'Region'
                          ? navigatorKey.currentState!.context.width * 0.0155
                          : columnName == 'Model'
                              ? navigatorKey.currentState!.context.width * 0.011
                              : columnName == 'CPU'
                                  ? navigatorKey.currentState!.context.width *
                                      0.01
                                  : columnName == 'Qloud Score'
                                      ? getCurrentScreenSizeForColumn()
                                      : 0,
          right: columnName == 'Uptime'
              ? navigatorKey.currentState!.context.width * 0.02
              : 0,
        ),
        alignment: columnName == 'Uptime'
            ? Alignment.centerRight
            : Alignment.centerLeft,
        child: Text(
          columnName,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  double getCurrentScreenSizeForColumn() {
    return navigatorKey.currentState!.context.isDesktop
        ? navigatorKey.currentState!.context.width >= 2250
            ? navigatorKey.currentState!.context.width * 0.0083
            : navigatorKey.currentState!.context.width * 0.01
        : navigatorKey.currentState!.context.width * 0.01;
  }

  Widget _buildPagination() {
    final totalPages = _findTotalPages();
    final pageButtons = <Widget>[];
    var dotsAdded = false; // Flag to track if dots have been added

    for (var i = 0; i < totalPages; i++) {
      if (i < 3 ||
          i >= totalPages - 3 ||
          (pageIndex - 1 <= i && pageIndex + 1 >= i)) {
        pageButtons.add(_buildPageButton(i));
        dotsAdded = false; // Reset flag when a regular page button is added
      } else if (!dotsAdded) {
        // Add dots only if they haven't been added in the last iteration
        pageButtons.add(_buildDots());
        dotsAdded = true; // Set flag to true when dots are added
      }
    }

    return context.isDesktop
        ? Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildPreviousButton(),
                Row(children: pageButtons),
                _buildNextButton(),
              ],
            ),
          )
        : Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: pageButtons,
              ),
              const Space.vertical(16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildPreviousButton(),
                  _buildNextButton(),
                ],
              ),
            ],
          );
  }

  int _findTotalPages() {
    if (_searchController.text.trim().isEmpty && _selectedRegion == 'All') {
      return (context.read<ServerDataProvider>().systemMetrics.devicesOnline /
              rowsPerPage)
          .ceil();
    } else {
      return context.read<ServerDataProvider>().numberOfPages;
    }
  }

  Widget _buildPageButton(int index) {
    return SizedBox(
      width: pageIndex == index
          ? pageIndex >= 10
              ? 50
              : 50
          : 50,
      height: pageIndex == index
          ? pageIndex >= 10
              ? 50
              : 50
          : 50,
      child: CustomGradientButton(
        title: '${index + 1}',
        onTap: () {
          _onPageChangeCall(index);
        },
        hideGlow: pageIndex != index,
        hideContainerColor: pageIndex != index,
        addShadow: pageIndex == index,
        radius: 1000,
      ),
    );
  }

  Widget _buildDots() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 4),
      child: Text(
        '...',
        style: TextStyle(color: Colors.white),
      ),
    );
  }

  bool isHover = false;
  Widget _buildPreviousButton() {
    return SizedBox(
      height: 50,
      child: CustomGradientButton(
        title: 'Previous',
        fontSize: 14,
        preFix: const Icon(
          Icons.arrow_back_rounded,
          size: 14,
        ),
        onTap: pageIndex > 0
            ? () {
                _onPageChangeCall(pageIndex - 1);
              }
            : null,
        isEnable: pageIndex > 0,
      ),
    );
  }

  bool isHoverForward = false;

  Widget _buildNextButton() {
    final totalPages = _findTotalPages();
    return SizedBox(
      height: 50,
      child: CustomGradientButton(
        title: 'Next',
        fontSize: 14,
        isEnable: pageIndex < totalPages - 1,
        trailing: const Icon(
          Icons.arrow_forward_rounded,
          size: 14,
        ),
        onTap: pageIndex < totalPages - 1
            ? () => _onPageChangeCall(pageIndex + 1)
            : null,
      ),
    );
  }

  void _onPageChangeCall(
    int newPageIndex, {
    bool refresh = false,
  }) async {
    if (pageIndex == newPageIndex && !refresh) return;

    setState(() {
      pageIndex = newPageIndex;
    });
    context.read<ServerDataProvider>().pageNumber = newPageIndex;
    final nftId = _searchController.text.trim().isEmpty
        ? null
        : _searchController.text.trim();
    // NFT ID: $nftId
    await context.read<ServerDataProvider>().fetchServersDataFiltered(
          page: newPageIndex + 1,
          limit: rowsPerPage,
          online: _selectedStatus == 'All' ? null : _selectedStatus == 'Online',
          region: _selectedRegion == 'All' ? null : _selectedRegion,
          nftId: nftId,
        );

    providerData = _getPageData(_filteredData, pageIndex, rowsPerPage);
    _providerDataSource = ProviderDataSource(providerData: providerData);
    if (context.read<ServerDataProvider>().tableData.isEmpty) {
      _onPageChangeCall(0);
    }
  }
}

class ProviderModel {
  ProviderModel({
    required this.name,
    required this.location,
    required this.uptime,
    required this.cpu,
    required this.gpu,
    required this.memory,
    required this.disk,
    required this.audited,
    required this.price,
    required this.model,
    required this.online,
    required this.qloudscore,
    required this.licenseImage,
    required this.serverId,
  });

  final String name;
  final String location;
  final String uptime;
  final String cpu;
  final String gpu;
  final String memory;
  final String disk;
  final String audited;
  final String price;
  final String model;
  final bool online;
  final String qloudscore;
  final String licenseImage;
  final String serverId;

  @override
  String toString() {
    return 'ProviderModel(name: $name, location: $location, uptime: $uptime, cpu: $cpu, gpu: $gpu, memory: $memory, disk: $disk, audited: $audited, price: $price, model: $model, online: $online, qloudscore: $qloudscore, licenseImage: $licenseImage, serverId: $serverId)';
  }
}

class ProviderDataSource extends DataGridSource {
  ProviderDataSource({required List<ProviderModel> providerData}) {
    _providerData = providerData
        .map<DataGridRow>(
          (data) => DataGridRow(
            cells: [
              DataGridCell<String>(
                columnName: 'name',
                value: data.name,
              ),
              DataGridCell<String>(columnName: 'region', value: data.location),
              DataGridCell<String>(columnName: 'model', value: data.model),
              DataGridCell<String>(columnName: 'cpu', value: data.cpu),
              DataGridCell<String>(
                columnName: 'qloudScore',
                value: data.qloudscore,
              ),
              DataGridCell<String>(columnName: 'memory', value: data.memory),
              DataGridCell<String>(
                columnName: 'status',
                value: data.online ? 'true' : 'false',
              ),
              DataGridCell<String>(columnName: 'uptime', value: data.uptime),
            ],
          ),
        )
        .toList();
  }

  List<DataGridRow> _providerData = [];

  @override
  List<DataGridRow> get rows => _providerData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    final index = _providerData.indexOf(row);

    Color backgroundColor;
    if (index % 2 == 0) {
      backgroundColor = Colors.white.withOpacity(0.04);
    } else {
      backgroundColor = Colors.white.withOpacity(0.1);
    }

    return DataGridRowAdapter(
      color: backgroundColor,
      cells: row.getCells().map<Widget>((cell) {
        if (cell.columnName == 'status') {
          final isOnline = cell.value == 'true';
          return Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(
              right: 8,
              top: 8,
              bottom: 8,
              left: navigatorKey.currentState!.context.width * 0.028,
            ),
            child: Row(
              children: [
                OnlineServerIndicator(
                  isOnline: isOnline,
                ),
                const Space.horizontal(4),
                Text(
                  isOnline ? 'Online' : 'Offline',
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: isOnline ? tilePercentage : deviceOfflineDarkColor,
                    fontSize:
                        navigatorKey.currentState!.context.isMobile ? 12 : 14,
                  ),
                ),
              ],
            ),
          );
        } else if (cell.columnName == 'uptime') {
          return Container(
            alignment: Alignment.centerRight,
            padding: EdgeInsets.only(
              right: navigatorKey.currentState!.context.width * 0.0255,
              top: 8,
              bottom: 8,
              left: 8,
            ),
            child: Text(
              '${double.parse(cell.value).toStringAsFixed(2)}%',
              style: TextStyle(
                fontWeight: FontWeight.w300,
                color: kWhite,
                fontSize: navigatorKey.currentState!.context.isMobile ? 12 : 14,
              ),
            ),
          );
        } else if (cell.columnName == 'memory') {
          return Container(
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                Space.horizontal(
                  navigatorKey.currentState!.context.width * 0.025,
                ),
                SvgPicture.asset(
                  'assets/icons/svg/server.svg',
                  height: navigatorKey.currentState!.context.isMobile ? 14 : 16,
                  width: navigatorKey.currentState!.context.isMobile ? 14 : 16,
                ),
                const Space.horizontal(4),
                Builder(
                  builder: (context) {
                    final value = cell.value.toString();
                    return SizedBox(
                      width: 50,
                      child: Text(
                        value.isNotEmpty ? '$value GB' : value,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w300,
                          fontSize: navigatorKey.currentState!.context.isMobile
                              ? 12
                              : 14,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          );
        } else if (cell.columnName == 'qloudScore') {
          return Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(
              right: 8,
              top: 8,
              bottom: 8,
              left: navigatorKey.currentState!.context.isDesktop
                  ? navigatorKey.currentState!.context.width >= 2250
                      ? navigatorKey.currentState!.context.width * 0.016
                      : navigatorKey.currentState!.context.width * 0.020
                  : navigatorKey.currentState!.context.width * 0.020,
            ),
            child: Row(
              children: [
                SvgPicture.asset(
                  Assets.svgQloudScoreCloudIcon,
                  height: navigatorKey.currentState!.context.isMobile ? 16 : 18,
                  width: navigatorKey.currentState!.context.isMobile ? 16 : 18,
                ),
                const Space.horizontal(4),
                SizedBox(
                  width: 45,
                  child: Text(
                    cell.value.toString(),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.right,
                    style: TextStyle(
                      fontWeight: FontWeight.w300,
                      color: Colors.white,
                      fontSize:
                          navigatorKey.currentState!.context.isMobile ? 12 : 14,
                    ),
                  ),
                ),
              ],
            ),
          );
        } else if (cell.columnName == 'cpu') {
          return Container(
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                Space.horizontal(
                  navigatorKey.currentState!.context.width * 0.01425,
                ),
                SvgPicture.asset(
                  'assets/icons/svg/cpu_meter_icon.svg',
                  height: navigatorKey.currentState!.context.isMobile ? 14 : 16,
                  width: navigatorKey.currentState!.context.isMobile ? 14 : 16,
                ),
                const Space.horizontal(10),
                Text(
                  cell.value.toString(),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.right,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w300,
                    fontSize:
                        navigatorKey.currentState!.context.isMobile ? 12 : 14,
                  ),
                ),
              ],
            ),
          );
        } else if (cell.columnName == 'location') {
          return Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(8),
            child: Text(
              cell.value.toString(),
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w300,
                fontSize: navigatorKey.currentState!.context.isMobile ? 12 : 14,
              ),
            ),
          );
        } else if (cell.columnName == 'name') {
          final values = cell.value.toString().split(' ');
          final id = values[0];
          final image = values[1];

          final url = '$image&img-width=26&img-height=26';

          return Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(8),
            margin: EdgeInsets.only(
              left: navigatorKey.currentState!.context.isDesktopSmall ||
                      navigatorKey.currentState!.context.isDesktopExtraSmall
                  ? navigatorKey.currentState!.context.width * 0.014
                  : navigatorKey.currentState!.context.width * 0.016,
            ),
            child: Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Container(
                    height: navigatorKey.currentState!.context.isDesktopSmall
                        ? 23
                        : 25,
                    width: navigatorKey.currentState!.context.isDesktopSmall
                        ? 23
                        : 25,
                    decoration: const BoxDecoration(
                      color: kBackgroundColor,
                    ),
                    child: Image.network(
                      url,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) {
                          return child;
                        }
                        return const Center(
                          child: SizedBox(
                            height: 5,
                            width: 5,
                            child: CircularProgressIndicator(
                              strokeWidth: 1.5,
                            ),
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) =>
                          const Icon(Icons.error),
                    ),
                  ),
                ),
                const Space.horizontal(8),
                Text(
                  id,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w300,
                    fontSize:
                        navigatorKey.currentState!.context.isMobile ? 12 : 14,
                  ),
                ),
              ],
            ),
          );
        } else if (cell.columnName == 'model') {
          return Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(8),
            margin: EdgeInsets.only(
              left: navigatorKey.currentState!.context.width * 0.0112,
            ),
            child: Text(
              cell.value.toString(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w300,
                fontSize: navigatorKey.currentState!.context.isMobile ? 12 : 14,
              ),
            ),
          );
        } else if (cell.columnName == 'region') {
          return Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(8),
            margin: EdgeInsets.only(
              left: navigatorKey.currentState!.context.width * 0.0157,
            ),
            child: Text(
              cell.value.toString(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w300,
                fontSize: navigatorKey.currentState!.context.isMobile ? 12 : 14,
              ),
            ),
          );
        } else {
          return Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.all(8),
            child: Text(
              cell.value.toString(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w300,
                fontSize: navigatorKey.currentState!.context.isMobile ? 12 : 14,
              ),
            ),
          );
        }
      }).toList(),
    );
  }
}
