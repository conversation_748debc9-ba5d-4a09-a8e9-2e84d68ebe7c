// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/log.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/ui/widgets/blurred_background.dart';
import 'package:nexqloud/core/ui/widgets/custom_gradient_button.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/features/main/utils/url_launcher.dart';

class MainAppBar extends StatelessWidget {
  const MainAppBar({
    super.key,
    required this.controller,
    required this.toggleMenu,
  });
  final VoidCallback toggleMenu;
  final AnimationController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: LayoutBuilder(
        builder: (context, constraint) {
          return (context.isDesktop || context.isDesktopLarge)
              ? Container(
                  padding: const EdgeInsets.only(top: 30),
                  width: context.horizontalPadding,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CupertinoButton(
                        onPressed: () => UrlLauncher.openViewName(''),
                        padding: EdgeInsets.zero,
                        minSize: 0,
                        child: SvgPicture.asset(
                          'assets/icons/svg/nex_logo.svg',
                          height: 32,
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(left: 10),
                        decoration: BoxDecoration(
                          border: GradientBoxBorder(
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF4e65b3).withOpacity(0.8),
                                kTransparent,
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                          ),
                          borderRadius: BorderRadius.circular(1000),
                        ),
                        height: 54,
                        child: BlurredBackground(
                          blurColorFilter: kWhite.withOpacity(0.2),
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              TextHoverButton(
                                onPressed: () => UrlLauncher.openViewName(
                                  'hardware-contributers',
                                ),
                                text: 'Link Device',
                              ),
                              const Space.horizontal(15),
                              TextHoverButton(
                                onPressed: () =>
                                    UrlLauncher.openViewName('cloud-computing'),
                                text: 'Elastic Computing',
                              ),
                              const Space.horizontal(15),
                              TextHoverButton(
                                onPressed: () => UrlLauncher.openViewName(
                                  'web-hosting',
                                ),
                                text: 'Web Hosting',
                              ),
                              const Space.horizontal(15),
                              TextHoverButton(
                                onPressed: () => UrlLauncher.openViewName(
                                  'strategic-partners',
                                ),
                                text: 'Partners',
                              ),
                              const Space.horizontal(15),
                              TextHoverButton(
                                onPressed: () => UrlLauncher.openViewName(
                                  '#faqs',
                                ),
                                text: 'FAQs',
                              ),
                            ],
                          ),
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(left: 10),
                        width: 120,
                      ),
                      if (false)
                        Container(
                          margin: const EdgeInsets.only(left: 10),
                          child: Row(
                            children: [
                              TextButton(
                                style: ButtonStyle(
                                  backgroundColor:
                                      WidgetStateProperty.all(kTransparent),
                                ),
                                onPressed: null,
                                child: Text(
                                  'Sign up',
                                  style: context.medium,
                                ),
                              ),
                              const Space.horizontal(22),
                              SizedBox(
                                width: 120,
                                child: CustomGradientButton(
                                  title: 'Sign In',
                                  fontSize: 14,
                                  onTap: () {
                                    'Sign In'.printInfo();
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                )
              : (context.isDesktop || context.isDesktopLarge) &&
                      context.width >= 1190 &&
                      !context.isDesktopSmall
                  ? Container(
                      padding: const EdgeInsets.only(top: 30),
                      width: context.horizontalPadding,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CupertinoButton(
                            onPressed: () => UrlLauncher.openViewName(''),
                            padding: EdgeInsets.zero,
                            minSize: 0,
                            child: SvgPicture.asset(
                              'assets/icons/svg/nex_logo.svg',
                              height: 32,
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(left: 20),
                            decoration: BoxDecoration(
                              border: GradientBoxBorder(
                                gradient: LinearGradient(
                                  colors: [
                                    const Color(0xFF4e65b3).withOpacity(0.8),
                                    kTransparent,
                                  ],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                ),
                              ),
                              borderRadius: BorderRadius.circular(1000),
                            ),
                            height: 54,
                            child: BlurredBackground(
                              blurColorFilter: kWhite.withOpacity(0.2),
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  TextHoverButton(
                                    onPressed: () => UrlLauncher.openViewName(
                                      'hardware-contributers',
                                    ),
                                    text: 'Link Device',
                                  ),
                                  const Space.horizontal(16),
                                  TextHoverButton(
                                    onPressed: () => UrlLauncher.openViewName(
                                      'cloud-computing',
                                    ),
                                    text: 'Elastic Computing',
                                  ),
                                  const Space.horizontal(16),
                                  TextHoverButton(
                                    onPressed: () => UrlLauncher.openViewName(
                                      'web-hosting',
                                    ),
                                    text: 'Web Hosting',
                                  ),
                                  const Space.horizontal(16),
                                  TextHoverButton(
                                    onPressed: () => UrlLauncher.openViewName(
                                      'strategic-partners',
                                    ),
                                    text: 'Partners',
                                  ),
                                  const Space.horizontal(16),
                                  TextHoverButton(
                                    onPressed: () => UrlLauncher.openViewName(
                                      '#faqs',
                                    ),
                                    text: 'FAQs',
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(left: 10),
                            child: Row(
                              children: [
                                TextButton(
                                  style: ButtonStyle(
                                    backgroundColor:
                                        WidgetStateProperty.all(kTransparent),
                                  ),
                                  onPressed: null,
                                  child: Text(
                                    'Sign up',
                                    style: context.normal,
                                  ),
                                ),
                                const Space.horizontal(24),
                                SizedBox(
                                  width: 120,
                                  child: CustomGradientButton(
                                    title: 'Sign In',
                                    onTap: () {
                                      'Sign In'.printInfo();
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                  : Padding(
                      padding: EdgeInsets.only(
                        left: context.width * 0.07,
                        right: context.width * 0.05,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CupertinoButton(
                            onPressed: () => UrlLauncher.openViewName(''),
                            padding: EdgeInsets.zero,
                            minSize: 0,
                            child: SvgPicture.asset(
                              'assets/icons/svg/nex_logo.svg',
                              height: 30,
                            ),
                          ),
                          // if (kDebugMode)
                          //   Text(
                          //     context.vHeight.toString(),
                          //     style: const TextStyle(color: kWhite),
                          //   ),
                          // if (kDebugMode)
                          //   Text(
                          //     context.vWidth.toString(),
                          //     style: const TextStyle(color: kWhite),
                          //   ),
                          IconButton(
                            onPressed: () {
                              toggleMenu();
                            },
                            icon: AnimatedIcon(
                              icon: AnimatedIcons.menu_close,
                              progress: controller,
                              color: kWhite,
                              size: 30,
                            ),
                          ),
                        ],
                      ),
                    );
        },
      ),
    );
  }
}

class TextHoverButton extends StatelessWidget {
  const TextHoverButton({
    super.key,
    required this.text,
    this.onPressed,
    this.defaultColor,
    this.hoverColor,
    this.fontSize,
  });
  final String text;
  final VoidCallback? onPressed;
  final Color? defaultColor;
  final Color? hoverColor;
  final double? fontSize;

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      style: ButtonStyle(
        padding: WidgetStateProperty.all(EdgeInsets.zero),
        overlayColor: const WidgetStatePropertyAll(kTransparent),
        foregroundColor: WidgetStateProperty.resolveWith<Color>(
          (states) {
            if (states.contains(WidgetState.hovered)) {
              return hoverColor ?? Colors.grey;
            }
            return defaultColor ?? kWhite;
          },
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: fontSize ?? 16,
        ),
      ),
    );
  }
}
