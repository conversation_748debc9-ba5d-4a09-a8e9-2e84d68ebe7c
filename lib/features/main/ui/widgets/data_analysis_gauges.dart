import 'package:animate_do/animate_do.dart';
import 'package:flutter/material.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/constants/env_constants.dart';
import 'package:nexqloud/core/extensions/double.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/ui/widgets/invite_status_tile.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/core/utils/data_size.dart';
import 'package:nexqloud/core/utils/utils.dart';
import 'package:nexqloud/features/main/providers/server_data_provider.dart';
import 'package:nexqloud/features/main/ui/widgets/custom_line_chart.dart';
import 'package:nexqloud/features/main/ui/widgets/glow_arc_painter.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

class DataAnalysisGauges extends StatelessWidget {
  const DataAnalysisGauges({super.key});

  @override
  Widget build(BuildContext context) {
    final isLiveViewOnly = EnvConstants.appEnv == Env.LIVE;

    return Padding(
      padding: const EdgeInsets.only(
        top: 8,
      ),
      child: Container(
        width: (context.isDesktop || context.isDesktopLarge)
            ? (isLiveViewOnly ? context.width : context.horizontalPadding)
            : null,
        margin: !isLiveViewOnly
            ? null
            : const EdgeInsets.only(
                left: 24,
                right: 24,
              ),
        child: context.isMobile || context.isTablet
            ? const MobileAnalysisCards()
            : const DesktopAnalysisCards(),
      ),
    );
  }
}

class DesktopAnalysisCards extends StatefulWidget {
  const DesktopAnalysisCards({super.key});

  @override
  DesktopAnalysisCardsState createState() => DesktopAnalysisCardsState();
}

class DesktopAnalysisCardsState extends State<DesktopAnalysisCards> {
  // Define the dynamic data for the gauges
  double diskReadsProgress = 0;
  double diskWritesProgress = 0;
  double cpuProgress = 0;
  double ramProgress = 0;
  double networkOutboundProgress = 0;
  double networkInboundProgress = 0;
  double devicesOnline = 0;

  // Mock function to simulate data updates
  // void updateGaugeData() {
  //   setState(() {
  //     diskReadsProgress = (diskReadsProgress + Random().nextInt(10) - 5)
  //         .clamp(10, 90)
  //         .toDouble();
  //     diskWritesProgress = (diskWritesProgress + Random().nextInt(10) - 5)
  //         .clamp(10, 90)
  //         .toDouble();
  //     cpuProgress =
  //         (cpuProgress + Random().nextInt(10) - 5).clamp(40, 90).toDouble();
  //     ramProgress =
  //         (ramProgress + Random().nextInt(10) - 5).clamp(20, 90).toDouble();
  //     networkOutboundProgress =
  //         (networkOutboundProgress + Random().nextInt(10) - 5)
  //             .clamp(10, 90)
  //             .toDouble();
  //     networkInboundProgress =
  //         (networkInboundProgress + Random().nextInt(10) - 5)
  //             .clamp(10, 90)
  //             .toDouble();
  //   });
  // }

  // Timer? _timer;

  // @override
  // void initState() {
  //   super.initState();
  //   _serverDataProvider = context.read<ServerDataProvider>();
  //   // final data = context.read<ServerDataProvider>().systemMetrics;
  //   // updateData(data);

  //   // _timer = Timer.periodic(const Duration(seconds: 30), (timer) {
  //   //   context.read<ServerDataProvider>().fetchSystemMetrics();
  //   //   final data = context.read<ServerDataProvider>().systemMetrics;
  //   //   updateData(data);
  //   // });
  // }

  // void updateData(SystemMetrics data) {
  //   diskReadsProgress = data.diskRead;
  //   diskWritesProgress = data.diskWrite;
  //   cpuProgress = data.cpuUtilization;
  //   ramProgress = data.ramUtilization;
  //   networkOutboundProgress = data.networkOutbound ?? 0;
  //   networkInboundProgress = data.networkInbound ?? 0;
  //   totalDevices = data.devicesOnline ?? 0;
  //   setState(() {});
  // }

  // String convertToGBFromKB(double bytes) {
  //   final gb = bytes / (1024 * 1024);
  //   return gb.toStringAsFixed(0);
  // }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ServerDataProvider>();
    final isLiveViewOnly = EnvConstants.appEnv == Env.LIVE;

    return Consumer<ServerDataProvider>(
      builder: (context, _, child) {
        final data = provider.systemMetrics;

        diskReadsProgress = data.percentages.diskReadPercentile;
        diskWritesProgress = data.percentages.diskWritePercentile;
        cpuProgress = data.percentages.cpuUtilizationPercentile;
        ramProgress = data.percentages.ramUtilizationPercentile;
        networkOutboundProgress =
            data.percentages.networkOutboundPercentile ?? 0;
        networkInboundProgress = data.percentages.networkInboundPercentile ?? 0;
        devicesOnline = data.devicesOnline ?? 0;

        return Column(
          children: [
            SizedBox(
              // height: context.width * 0.112,
              height: isLiveViewOnly
                  ? context.width * 0.162
                  : context.sizeRatio * 0.075,

              child: Row(
                children: [
                  Expanded(
                    child: CircularGauge(
                      value: data.networkInbound.toStringAsFixed(2),
                      progress: networkInboundProgress,
                      unit: 'GBit/s',
                      title: 'Network Inbound',
                      subCardTitle: 'Devices Online',
                      subCardValue: '$devicesOnline',
                    ),
                  ),
                  const Space.horizontal(8),
                  Expanded(
                    child: CircularGauge(
                      value: data.networkOutbound.toStringAsFixed(2),
                      progress: networkOutboundProgress,
                      unit: 'GBit/s',
                      title: 'Network Outbound',
                      subCardTitle: "Total GPU's",
                      subCardValue: data.totalGPUs.toString(),
                    ),
                  ),
                  const Space.horizontal(8),
                  SizedBox(
                    width: isLiveViewOnly
                        ? context.width * 0.17
                        : context.isDesktop
                            ? context.sizeRatio * 0.078
                            : 270,
                    child: NeedleCircularGauge(
                      progress: cpuProgress,
                      unit: 'vCPU Utilization',
                      value: 'CPU',
                    ),
                  ),
                  const Space.horizontal(8),
                  SizedBox(
                    width: isLiveViewOnly
                        ? context.width * 0.17
                        : context.isDesktop
                            ? context.sizeRatio * 0.078
                            : 270,
                    // width: context.isDesktop ? context.width * 0.12 : 270,
                    child: NeedleCircularGauge(
                      value: 'RAM',
                      progress: ramProgress,
                      unit: 'RAM Utilization',
                    ),
                  ),
                  const Space.horizontal(8),
                  Expanded(
                    child: CircularGauge(
                      value: formatBytes(
                        adjustBytes(data.diskRead),
                        showUnits: false,
                      ),
                      progress: diskReadsProgress,
                      unit: dataSizeUnit(adjustBytes(data.diskRead)),
                      title: 'Disk Reads',
                      subCardTitle: 'Avg. Bandwidth',
                      subCardValue: data.networkAvgBandwidth.toStringAsFixed(2),
                    ),
                  ),
                  const Space.horizontal(8),
                  Expanded(
                    child: CircularGauge(
                      value: formatBytes(
                        adjustBytes(data.diskWrite),
                        showUnits: false,
                      ),
                      progress: diskWritesProgress,
                      unit: dataSizeUnit(adjustBytes(data.diskRead)),
                      title: 'Disk Writes',
                      subCardTitle: 'Total vCPU',
                      subCardValue: data.totalVCPUCores.toK,
                    ),
                  ),
                ],
              ),
            ),
            const Space.vertical(8),

            /////////////
            Row(
              children: [
                Expanded(
                  child: GaugesSubInformationCard(
                    value: data.devicesOnline.toString(),
                    description: 'Devices Online',
                    performanceValue: networkInboundProgress ?? 0.0,
                  ),
                ),
                const Space.horizontal(8),
                Expanded(
                  child: GaugesSubInformationCard(
                    value: data.totalGPUs.toString(),
                    description: "Total GPU's",
                    performanceValue: networkOutboundProgress ?? 0.0,
                  ),
                ),
                const Space.horizontal(8),
                SizedBox(
                  width: isLiveViewOnly
                      ? context.width * 0.17
                      : context.sizeRatio * 0.078,
                  child: GaugesSubInformationCard(
                    value: '${double.parse(cpuProgress.toStringAsFixed(2))}%',
                    description:
                        '${((cpuProgress / 100) * data.totalCPU).ceil()}/${data.totalVCPUCores.ceil()} vCPUs',
                    performanceValue: cpuProgress ?? 0.0,
                  ),
                ),
                const Space.horizontal(8),
                SizedBox(
                  width: isLiveViewOnly
                      ? context.width * 0.17
                      : context.sizeRatio * 0.078,
                  child: GaugesSubInformationCard(
                    value: '${ramProgress.toStringAsFixed(2)}%',
                    description:
                        '${formatBytes(data.totalMemoryUsed)}/${formatBytes(data.totalMemory)}',
                    performanceValue: ramProgress ?? 0.0,
                  ),
                ),
                const Space.horizontal(8),
                Expanded(
                  child: GaugesSubInformationCard(
                    value: data.networkAvgBandwidth.toStringAsFixed(2),
                    description: 'Avg. Bandwidth\nGBit/s',
                    performanceValue: diskReadsProgress ?? 0.0,
                  ),
                ),
                const Space.horizontal(8),
                Expanded(
                  child: GaugesSubInformationCard(
                    value: data.totalVCPUCores.toK,
                    description: 'Total vCPUs',
                    performanceValue: diskWritesProgress ?? 0.0,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}

class MobileAnalysisCards extends StatefulWidget {
  const MobileAnalysisCards({super.key});

  @override
  State<MobileAnalysisCards> createState() => _MobileAnalysisCardsState();
}

class _MobileAnalysisCardsState extends State<MobileAnalysisCards> {
  // Define the dynamic data for the gauges
  double diskReadsProgress = 0;
  double diskWritesProgress = 0;
  double cpuProgress = 0;
  double ramProgress = 0;
  double networkOutboundProgress = 0;
  double networkInboundProgress = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ServerDataProvider>();

    return Consumer<ServerDataProvider>(
      builder: (context, _, child) {
        final data = provider.systemMetrics;

        diskReadsProgress = data.percentages.diskReadPercentile;
        diskWritesProgress = data.percentages.diskWritePercentile;
        cpuProgress = data.percentages.cpuUtilizationPercentile;
        ramProgress = data.percentages.ramUtilizationPercentile;
        networkOutboundProgress =
            data.percentages.networkOutboundPercentile ?? 0;
        networkInboundProgress = data.percentages.networkInboundPercentile ?? 0;
        final devicesOnline = data.devicesOnline ?? 0;

        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: kWhite.withOpacity(0.05),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: kWhite.withOpacity(0.2), width: 0.5),
          ),
          child: Column(
            children: [
              Container(
                height: 5,
                width: 153,
                decoration: BoxDecoration(
                  color: kWhite,
                  borderRadius: BorderRadius.circular(50),
                ),
                margin: const EdgeInsets.only(bottom: 12),
              ),
              SizedBox(
                height: context.isMobileOrientationWide
                    ? context.sizeRatio * 0.2
                    : context.isMobile
                        ? context.sizeRatio * 0.15
                        : context.sizeRatio * 0.16,
                child: Row(
                  children: [
                    Expanded(
                      child: NeedleCircularGauge(
                        progress: cpuProgress,
                        unit: 'vCPU  Utilization',
                        value: '${data.cpuUtilization.toStringAsFixed(2)}%',
                      ),
                    ),
                    const Space.horizontal(8),
                    Expanded(
                      child: NeedleCircularGauge(
                        value: '${data.ramUtilization.toStringAsFixed(2)}%',
                        progress: data.ramUtilization,
                        unit: 'RAM Utilization',
                      ),
                    ),
                  ],
                ),
              ),
              const Space.vertical(10),
              Row(
                children: [
                  Expanded(
                    child: GaugesSubInformationCard(
                      value: devicesOnline.toString(),
                      description: 'Devices Online',
                    ),
                  ),
                  const Space.horizontal(8),
                  Expanded(
                    child: GaugesSubInformationCard(
                      value: data.totalGPUs.toString(),
                      description: "Total GPU's",
                    ),
                  ),
                ],
              ),
              const Space.vertical(10),
              Row(
                children: [
                  Expanded(
                    child: GaugesSubInformationCard(
                      value: data.networkAvgBandwidth.toStringAsFixed(2),
                      description: 'Avg. Bandwidth',
                    ),
                  ),
                  const Space.horizontal(8),
                  Expanded(
                    child: GaugesSubInformationCard(
                      value: data.totalVCPUCores.toK,
                      description: 'Total vCPU',
                    ),
                  ),
                ],
              ),
              const Space.vertical(10),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: kWhite.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(20),
                  border:
                      Border.all(color: kWhite.withOpacity(0.2), width: 0.5),
                ),
                child: Column(
                  children: [
                    InviteStatusTile(
                      title: 'Network Inbound',
                      value: data.networkInbound.toStringAsFixed(2) ?? '',
                      unit: 'GBit/s',
                      progress: networkInboundProgress,
                    ),
                    InviteStatusTile(
                      title: 'Network Outbound',
                      value: data.networkOutbound.toStringAsFixed(2) ?? '',
                      unit: 'GBit/s',
                      progress: networkOutboundProgress,
                    ),
                    InviteStatusTile(
                      title: 'Disk Reads',
                      value: formatBytes(
                        adjustBytes(data.diskRead),
                        showUnits: false,
                      ),
                      unit: dataSizeUnit(adjustBytes(data.diskRead)),
                      progress: diskReadsProgress,
                    ),
                    InviteStatusTile(
                      title: 'Disk Writes',
                      value: formatBytes(
                        adjustBytes(data.diskWrite),
                        showUnits: false,
                      ),
                      unit: dataSizeUnit(adjustBytes(data.diskWrite)),
                      progress: diskWritesProgress,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class CircularGauge extends StatelessWidget {
  const CircularGauge({
    super.key,
    required this.value,
    required this.progress,
    required this.unit,
    required this.title,
    required this.subCardTitle,
    required this.subCardValue,
  });
  final String value;
  final double progress;
  final String unit;
  final String title;
  final String subCardTitle;
  final String subCardValue;

  @override
  Widget build(BuildContext context) {
    final isLiveViewOnly = EnvConstants.appEnv == Env.LIVE;
    return Stack(
      children: [
        Container(
          padding: const EdgeInsets.only(bottom: 26),
          decoration: BoxDecoration(
            color: kWhite.withOpacity(0.05),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: kWhite.withOpacity(0.2), width: 0.5),
          ),
          child: Column(
            children: [
              Expanded(
                child: SizedBox(
                  height: context.isDesktop
                      ? context.width * 0.1
                      : context.isTablet || context.isMobile
                          ? 185
                          : context.width * 0.4,
                  child: Column(
                    children: [
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.symmetric(
                            horizontal: context.isDesktop ? 10 : 0,
                            vertical: context.isTablet ? 20 : 0,
                          ),
                          child: SfRadialGauge(
                            axes: <RadialAxis>[
                              RadialAxis(
                                startAngle: -90,
                                endAngle: 270,
                                radiusFactor: context.isDesktop
                                    ? 0.9
                                    : context.isMobile
                                        ? 0.8
                                        : 0.88,
                                showFirstLabel: false,
                                showLabels: false,
                                showTicks: false,
                                annotations: [
                                  GaugeAnnotation(
                                    angle: 0, // Centered
                                    widget: FadeIn(
                                      delay: const Duration(milliseconds: 1700),
                                      child: LayoutBuilder(
                                        builder: (context, constraints) {
                                          final size = constraints.maxWidth;

                                          return SizedBox(
                                            width: size,
                                            height: size,
                                            child: CustomPaint(
                                              painter: GlowArcPainterCircular(
                                                progress: progress,
                                                glowColor: const Color(
                                                  0xFF33B1FF,
                                                ), // Your glow color
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                  // GaugeAnnotation(
                                  //   angle: 0, // Centered
                                  //   positionFactor: 0, // Centered
                                  //   widget: LayoutBuilder(
                                  //     builder: (context, constraints) {
                                  //       final size = constraints.maxWidth;
                                  //
                                  //       return SizedBox(
                                  //         width: size,
                                  //         height: size,
                                  //         child: CustomPaint(
                                  //           painter: InnerDepthPainter(
                                  //             progress: progress,
                                  //             depthColor:
                                  //                 Colors.white, // Depth color
                                  //           ),
                                  //         ),
                                  //       );
                                  //     },
                                  //   ),
                                  // ),
                                  GaugeAnnotation(
                                    widget: Stack(
                                      children: [
                                        Align(
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                value, //'4.126'
                                                style: context.bold?.copyWith(
                                                  fontSize: context.isTablet ||
                                                          context.isMobile
                                                      ? 42
                                                      : context.isDesktopLarge
                                                          ? context.sizeRatio *
                                                              0.009
                                                          : isLiveViewOnly
                                                              ? 32
                                                              : 24,
                                                ),
                                              ),
                                              Text(
                                                unit, //'GBit/s'
                                                style: context.normal?.copyWith(
                                                  fontSize: context.isTablet ||
                                                          context.isMobile
                                                      ? context.sizeRatio *
                                                          0.0115
                                                      : context.isDesktopLarge
                                                          ? context.sizeRatio *
                                                              0.004
                                                          : isLiveViewOnly
                                                              ? 20
                                                              : 12,
                                                  color:
                                                      const Color(0xFFBFBFBF),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                                axisLineStyle: AxisLineStyle(
                                  thickness: 0.2,
                                  color: kWhite.withOpacity(0.9),
                                  // color: const Color(0xFFEEEEEE).withOpacity(0.18),
                                  thicknessUnit: GaugeSizeUnit.factor,
                                ),
                                pointers: <GaugePointer>[
                                  // Background RangePointers to create depth effect
                                  RangePointer(
                                    value: 100, // Full circle
                                    width: 0.02,
                                    sizeUnit: GaugeSizeUnit.factor,
                                    color:
                                        kBlack.withOpacity(0.075), // Edge color
                                    // cornerStyle: CornerStyle.bothCurve,
                                  ),
                                  RangePointer(
                                    value: 100,
                                    width: 0.05,
                                    sizeUnit: GaugeSizeUnit.factor,
                                    color: kBlack.withOpacity(0.065),
                                    // cornerStyle: CornerStyle.bothCurve,
                                  ),
                                  RangePointer(
                                    value: 100,
                                    width: 0.07,
                                    sizeUnit: GaugeSizeUnit.factor,
                                    color: kBlack.withOpacity(0.04),
                                    // cornerStyle: CornerStyle.bothCurve,
                                  ),
                                  RangePointer(
                                    value: 100,
                                    width: 0.09,
                                    sizeUnit: GaugeSizeUnit.factor,
                                    color: kBlack.withOpacity(0.02),
                                    // cornerStyle: CornerStyle.bothCurve,
                                  ),

                                  ///Main ProgressPointer
                                  RangePointer(
                                    enableAnimation: true,
                                    animationDuration: 1600,
                                    value: progress, // 40
                                    width: 0.2,
                                    sizeUnit: GaugeSizeUnit.factor,
                                    cornerStyle: CornerStyle.bothCurve,
                                    gradient: getDynamicGaugeGradient(progress),
                                    // gradient: const SweepGradient(
                                    //   colors: <Color>[
                                    //     Color(0xFF33B1FF),
                                    //     Color(0xFF9933FF),
                                    //   ],
                                    //   stops: <double>[0.25, 0.75],
                                    // ),
                                  ),
                                  // Background RangePointers to create depth effect
                                  RangePointer(
                                    value: progress - 1,
                                    width: 0.09,
                                    sizeUnit: GaugeSizeUnit.factor,
                                    gradient: SweepGradient(
                                      colors: <Color>[
                                        kTransparent,
                                        kWhite.withOpacity(
                                          0.02,
                                        ), // Lowest opacity
                                        kWhite.withOpacity(0.02),
                                        kWhite.withOpacity(0.02),
                                        kWhite.withOpacity(0.02),
                                      ],
                                    ),
                                    cornerStyle: CornerStyle.bothCurve,
                                    enableAnimation: true,
                                  ),

                                  RangePointer(
                                    value: progress - 1,
                                    width: 0.07,
                                    sizeUnit: GaugeSizeUnit.factor,
                                    gradient: SweepGradient(
                                      colors: <Color>[
                                        kTransparent,
                                        kWhite.withOpacity(0.04),
                                        kWhite.withOpacity(0.04),
                                        kWhite.withOpacity(0.04),
                                        kWhite.withOpacity(0.04),
                                      ],
                                    ),
                                    cornerStyle: CornerStyle.bothCurve,
                                    enableAnimation: true,
                                  ),

                                  RangePointer(
                                    value: progress - 1,
                                    width: 0.05,
                                    sizeUnit: GaugeSizeUnit.factor,
                                    gradient: SweepGradient(
                                      colors: <Color>[
                                        kTransparent,
                                        kWhite.withOpacity(0.065),
                                        kWhite.withOpacity(0.065),
                                        kWhite.withOpacity(0.065),
                                        kWhite.withOpacity(0.065),
                                      ],
                                    ),
                                    cornerStyle: CornerStyle.bothCurve,
                                    enableAnimation: true,
                                  ),

                                  RangePointer(
                                    value: progress - 1,
                                    width: 0.02,
                                    sizeUnit: GaugeSizeUnit.factor,
                                    gradient: SweepGradient(
                                      colors: <Color>[
                                        kTransparent,
                                        kWhite.withOpacity(
                                          0.075,
                                        ), // Highest opacity
                                        kWhite.withOpacity(0.075),
                                        kWhite.withOpacity(0.075),
                                        kWhite.withOpacity(0.075),
                                      ],
                                    ),
                                    cornerStyle: CornerStyle.bothCurve,
                                    enableAnimation: true,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          bottom: context.isTablet ? 20 : 16,
          left: 0,
          right: 0,
          child: Container(
            alignment: Alignment.center,
            margin: const EdgeInsets.symmetric(
              horizontal: 12,
            ),
            child: Text(
              title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: context.normal?.copyWith(
                fontSize: context.isTablet || context.isMobile
                    ? context.sizeRatio * 0.01
                    : isLiveViewOnly
                        ? context.sizeRatio * 0.006
                        : context.sizeRatio * 0.005,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class NeedleCircularGauge extends StatelessWidget {
  const NeedleCircularGauge({
    super.key,
    required this.value,
    required this.progress,
    required this.unit,
  });
  final String value;
  final double progress;
  final String unit;

  @override
  Widget build(BuildContext context) {
    final isLiveViewOnly = EnvConstants.appEnv == Env.LIVE;
    return DecoratedBox(
      decoration: BoxDecoration(
        color: kWhite.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: kWhite.withOpacity(0.2), width: 0.5),
      ),
      child: Column(
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(
                vertical: context.isTablet ? 20 : 0,
              ),
              child: SfRadialGauge(
                axes: <RadialAxis>[
                  RadialAxis(
                    radiusFactor: context.isDesktop
                        ? 0.85
                        : context.isMobile
                            ? 0.85
                            : 1.0,
                    showFirstLabel: false,
                    showLabels: false,
                    tickOffset: 4,
                    minorTicksPerInterval: 3,
                    majorTickStyle: const MajorTickStyle(
                      length: 0.1,
                      color: kWhite,
                      lengthUnit: GaugeSizeUnit.factor,
                    ),
                    minorTickStyle: const MinorTickStyle(
                      length: 0.05,
                      color: kWhite,
                      lengthUnit: GaugeSizeUnit.factor,
                    ),
                    annotations: [
                      GaugeAnnotation(
                        angle: 90,
                        positionFactor: context.isTablet
                            ? 0.9
                            : context.isMobile
                                ? 0.9
                                : 0.67,
                        widget: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (context.width > 1200) const Space.vertical(15),
                            if (context.width > 1024 && context.width < 1200)
                              const Space.vertical(15),
                            Text(
                              value, //'4.126'
                              style: context.bold?.copyWith(
                                fontSize: (context.width > 1024)
                                    ? isLiveViewOnly
                                        ? context.sizeRatio * 0.008
                                        : context.sizeRatio * 0.007
                                    : context.isTablet
                                        ? 30
                                        : context.isMobile
                                            ? 16
                                            : 14,
                              ),
                            ),
                            if (context.width > 1400) const Space.vertical(6),
                            Text(
                              unit, //'GBit/s'
                              overflow: TextOverflow.ellipsis,
                              style: context.normal?.copyWith(
                                fontSize: (context.width > 1024)
                                    ? isLiveViewOnly
                                        ? context.sizeRatio * 0.006
                                        : context.sizeRatio * 0.005
                                    : context.isTablet || context.isMobile
                                        ? context.sizeRatio * 0.010
                                        : 12,
                                color: context.isDesktop
                                    ? kWhite
                                    : const Color(0xFFBFBFBF),
                              ),
                            ),
                            Space.vertical(context.isTablet ? 26 : 6),
                          ],
                        ),
                      ),
                      GaugeAnnotation(
                        angle: 0, // Centered
                        widget: FadeIn(
                          delay: const Duration(milliseconds: 1700),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              final size = constraints.maxWidth;

                              return SizedBox(
                                width: size,
                                height: size / 2,
                                child: CustomPaint(
                                  painter: GlowArcPainterHalfGauge(
                                    progress: progress,
                                    glowColor: const Color(
                                      0xFF33B1FF,
                                    ), // Your glow color
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                    axisLineStyle: AxisLineStyle(
                      thickness: 0.2,
                      color: kWhite.withOpacity(
                        (context.isDesktop || context.isDesktopSmall) &&
                                !(context.isMobile || context.isTablet)
                            ? 0.99
                            : 0.06,
                      ),
                      // color: const Color(0xFFEEEEEE).withOpacity(0.18),
                      thicknessUnit: GaugeSizeUnit.factor,
                      cornerStyle: CornerStyle.bothCurve,
                    ),
                    pointers: <GaugePointer>[
                      // RangePointer(
                      //   value: 99, // Full circle
                      //   width: 0.02,
                      //   sizeUnit: GaugeSizeUnit.factor,
                      //   color: kBlack.withOpacity(0.075), // Edge color
                      //   cornerStyle: CornerStyle.bothCurve,
                      // ),
                      RangePointer(
                        value: 99,
                        width: 0.04,
                        sizeUnit: GaugeSizeUnit.factor,
                        color: kBlack.withOpacity(0.065),
                        cornerStyle: CornerStyle.bothCurve,
                      ),
                      RangePointer(
                        value: 99,
                        width: 0.06,
                        sizeUnit: GaugeSizeUnit.factor,
                        color: kBlack.withOpacity(0.04),
                        cornerStyle: CornerStyle.bothCurve,
                      ),
                      RangePointer(
                        value: 99,
                        width: 0.07,
                        sizeUnit: GaugeSizeUnit.factor,
                        color: kBlack.withOpacity(0.03),
                        cornerStyle: CornerStyle.bothCurve,
                      ),
                      RangePointer(
                        value: 99,
                        width: 0.08,
                        sizeUnit: GaugeSizeUnit.factor,
                        color: kBlack.withOpacity(0.025),
                        cornerStyle: CornerStyle.bothCurve,
                      ),
                      RangePointer(
                        value: 99,
                        width: 0.09,
                        sizeUnit: GaugeSizeUnit.factor,
                        color: kBlack.withOpacity(0.02),
                        cornerStyle: CornerStyle.bothCurve,
                      ),
                      NeedlePointer(
                        enableAnimation: true,
                        animationDuration: 1600,
                        value: progress, // 40
                        needleColor: const Color(0xFF8052FF),
                        needleEndWidth: 4,
                        needleStartWidth: 0,
                        knobStyle: const KnobStyle(
                          color: Colors.white,
                        ),
                      ),
                      RangePointer(
                        enableAnimation: true,
                        animationDuration: 1600,
                        value: progress, // 40
                        width: 0.2,
                        sizeUnit: GaugeSizeUnit.factor,
                        cornerStyle: CornerStyle.bothCurve,
                        gradient: getDynamicGaugeGradient(progress),
                      ),
                      // Background RangePointers to create depth effect
                      RangePointer(
                        value: progress,
                        width: 0.09,
                        sizeUnit: GaugeSizeUnit.factor,
                        gradient: SweepGradient(
                          colors: <Color>[
                            kTransparent,
                            kWhite.withOpacity(0.025), // Lowest opacity
                            kWhite.withOpacity(0.025),
                            kWhite.withOpacity(0.025),
                            kWhite.withOpacity(0.025),
                            kWhite.withOpacity(0.025),
                            kWhite.withOpacity(0.025),
                            kWhite.withOpacity(0.01),
                          ],
                        ),
                        cornerStyle: CornerStyle.bothCurve,
                        enableAnimation: true,
                      ),

                      RangePointer(
                        value: progress - 1.5,
                        width: 0.07,
                        sizeUnit: GaugeSizeUnit.factor,
                        gradient: SweepGradient(
                          colors: <Color>[
                            kTransparent,
                            kWhite.withOpacity(0.04),
                            kWhite.withOpacity(0.04),
                            kWhite.withOpacity(0.04),
                            kWhite.withOpacity(0.04),
                            kWhite.withOpacity(0.04),
                            kWhite.withOpacity(0.04),
                            kWhite.withOpacity(0.02),
                          ],
                        ),
                        cornerStyle: CornerStyle.bothCurve,
                        enableAnimation: true,
                      ),

                      RangePointer(
                        value: progress - 1.5,
                        width: 0.05,
                        sizeUnit: GaugeSizeUnit.factor,
                        gradient: SweepGradient(
                          colors: <Color>[
                            kTransparent,
                            kWhite.withOpacity(0.065),
                            kWhite.withOpacity(0.065),
                            kWhite.withOpacity(0.065),
                            kWhite.withOpacity(0.065),
                            kWhite.withOpacity(0.065),
                            kWhite.withOpacity(0.065),
                            kWhite.withOpacity(0.04),
                          ],
                        ),
                        cornerStyle: CornerStyle.bothCurve,
                        enableAnimation: true,
                      ),

                      RangePointer(
                        value: progress - 1.5,
                        width: 0.02,
                        sizeUnit: GaugeSizeUnit.factor,
                        gradient: SweepGradient(
                          colors: <Color>[
                            kTransparent,
                            kWhite.withOpacity(0.075), // Highest opacity
                            kWhite.withOpacity(0.075),
                            kWhite.withOpacity(0.075),
                            kWhite.withOpacity(0.075),
                            kWhite.withOpacity(0.075),
                            kWhite.withOpacity(0.075),
                            kWhite.withOpacity(0.06),
                          ],
                        ),
                        cornerStyle: CornerStyle.bothCurve,
                        enableAnimation: true,
                      ),
                      // First glow layer
                      // RangePointer(
                      //   value: progress,
                      //   width: 0.22,
                      //   sizeUnit: GaugeSizeUnit.factor,
                      //   cornerStyle: CornerStyle.bothCurve,
                      //   gradient: SweepGradient(
                      //     colors: <Color>[
                      //       Color(0xFF33B1FF).withOpacity(0.17),
                      //       Color(0xFF9933FF).withOpacity(0.2),
                      //     ],
                      //     stops: <double>[0.25, 0.75],
                      //   ),
                      //   enableAnimation: true,
                      //   animationDuration: 1600,
                      // ),
                      // // Second glow layer
                      // RangePointer(
                      //   value: progress,
                      //   width: 0.23,
                      //   sizeUnit: GaugeSizeUnit.factor,
                      //   cornerStyle: CornerStyle.bothCurve,
                      //   gradient: SweepGradient(
                      //     colors: <Color>[
                      //       Color(0xFF33B1FF).withOpacity(0.14),
                      //       Color(0xFF9933FF).withOpacity(0.14),
                      //     ],
                      //     stops: <double>[0.25, 0.75],
                      //   ),
                      //   enableAnimation: true,
                      //   animationDuration: 1600,
                      // ),
                      // RangePointer(
                      //   value: progress,
                      //   width: 0.24,
                      //   sizeUnit: GaugeSizeUnit.factor,
                      //   cornerStyle: CornerStyle.bothCurve,
                      //   gradient: SweepGradient(
                      //     colors: <Color>[
                      //       Color(0xFF33B1FF).withOpacity(0.12),
                      //       Color(0xFF9933FF).withOpacity(0.12),
                      //     ],
                      //     stops: <double>[0.25, 0.75],
                      //   ),
                      //   enableAnimation: true,
                      //   animationDuration: 1600,
                      // ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // const Space.vertical(10),
          // SizedBox(
          //   height: context.isDesktop
          //       ? context.width * 0.130
          //       : context.isTablet
          //           ? 200
          //           : 200,
          //   child: Container(
          //     margin: const EdgeInsets.only(
          //       top: 8,
          //     ),
          //   ),
          // ),
          // Text(
          //   title,
          //   style: context.normal?.copyWith(
          //     fontSize: 12,
          //   ),
          // ),
        ],
      ),
    );
  }
}

class GaugesSubInformationCard extends StatelessWidget {
  const GaugesSubInformationCard({
    super.key,
    required this.value,
    required this.description,
    this.performanceValue = 0,
  });
  final String value;
  final String description;
  final double performanceValue;

  @override
  Widget build(BuildContext context) {
    final isLiveViewOnly = EnvConstants.appEnv == Env.LIVE;

    return DecoratedBox(
      decoration: BoxDecoration(
        color: kWhite.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: kWhite.withOpacity(0.2), width: 0.5),
      ),
      child: context.isMobile || context.isTablet
          ? Column(
              children: [
                Space.vertical(context.sizeRatio * 0.015),
                FittedBox(
                  child: Text(
                    value,
                    style: context.bold?.copyWith(
                      fontSize:
                          context.isDesktop ? 32 : context.sizeRatio * 0.02,
                    ),
                  ),
                ),
                Text(
                  description,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: context.normal?.copyWith(
                    fontSize: 14,
                    color: const Color(0xFFBFBFBF),
                  ),
                ),
                Space.vertical(context.sizeRatio * 0.015),
              ],
            )
          : Stack(
              children: [
                SizedBox(
                  height: isLiveViewOnly
                      ? 140
                      : context.isDesktop
                          ? 120
                          : context.sizeRatio * 0.132,
                  child: CustomLineChart(
                    value: performanceValue,
                  ),
                ),
                Align(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        Space.vertical(
                          context.isDesktop ? 22 : context.sizeRatio * 0.04,
                        ),
                        FittedBox(
                          child: Text(
                            value,
                            style: context.bold?.copyWith(
                              fontSize: context.isDesktop
                                  ? 32
                                  : context.sizeRatio * 0.032,
                            ),
                          ),
                        ),
                        Text(
                          description,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                          style: context.normal?.copyWith(
                            fontSize: context.isDesktop ? 14 : 16,
                            color: const Color(0xFFBFBFBF),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
