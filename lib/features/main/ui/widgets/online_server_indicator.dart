import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_shadow/flutter_inset_shadow.dart';
import 'package:nexqloud/core/constants/colors.dart';

class OnlineServerIndicator extends StatelessWidget {
  const OnlineServerIndicator({super.key, required this.isOnline});
  final bool isOnline;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    return Stack(
      children: [
        Container(
          height: 14,
          width: 14,
          margin: const EdgeInsets.only(right: 13),
          decoration: BoxDecoration(
            color: isOnline ? const Color(0xFF20FC8F) : offlineIndicatorColor,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                inset: true,
                color: Colors.black.withOpacity(0.56),
                // color: Color.fromRGBO(180, 186, 194, 0.9),
                blurRadius: 5, //extend the shadow
                offset: const Offset(-6, -3),
              ),
              BoxShadow(
                color: isOnline
                    ? const Color(0xFF20FC8F).withOpacity(0.4)
                    : offlineIndicatorColor.withOpacity(0.4),
                blurRadius: 5,
                spreadRadius: 5,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
