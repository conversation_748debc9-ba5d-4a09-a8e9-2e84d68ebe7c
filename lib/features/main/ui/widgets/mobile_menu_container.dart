import 'package:flutter/material.dart';

import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/features/main/ui/widgets/menu_item.dart';
import 'package:nexqloud/features/main/utils/url_launcher.dart';

class MobileMenuContainer extends StatelessWidget {
  const MobileMenuContainer({super.key, required this.menuMaxHeight});
  final double menuMaxHeight;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: menuMaxHeight,
      color: kBackgroundColor,
      padding: EdgeInsets.symmetric(
        vertical: 20,
        horizontal:
            context.isDesktop ? context.width * 0.189 : context.width * 0.07,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MenuItem(
            text: 'Link Device',
            onTap: () {
              UrlLauncher.openViewName('#overview');
            },
          ),
          MenuItem(
            text: 'Elastic Computing',
            onTap: () {
              UrlLauncher.openViewName('#features');
            },
          ),
          MenuItem(
            text: 'Web Hosting',
            onTap: () {
              UrlLauncher.openViewName('#built-for-all');
            },
          ),
          MenuItem(
            text: 'Partners',
            onTap: () {
              UrlLauncher.openViewName('strategic-partners');
            },
          ),
          MenuItem(
            text: 'FAQs',
            onTap: () {
              UrlLauncher.openViewName('#faqs');
            },
          ),
        ],
      ),
    );
  }
}
