import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/ui/widgets/blurred_background.dart';
import 'package:nexqloud/generated/assets.dart';

class VerticalZoomSlider extends StatelessWidget {
  const VerticalZoomSlider({
    super.key,
    required this.zoomValue,
    required this.onChanged,
    this.minZoom = 1.0,
    this.maxZoom = 10.0,
    this.isExpandedView = false,
  });
  final double zoomValue;
  final ValueChanged<double> onChanged;
  final double minZoom;
  final double maxZoom;
  final bool isExpandedView;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Plus button to increase zoom
        InkWell(
          onTap: () {
            onChanged(zoomValue + 1);
          },
          child: BlurredBackground(
            borderRadius: 8,
            child: Container(
              height: context.isTablet ? 42 : 32,
              width: context.isTablet ? 42 : 32,
              padding: const EdgeInsets.all(10),
              decoration: zoomValue >= 5
                  ? ShapeDecoration(
                      color: Colors.black.withOpacity(0.0001),
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          width: 0.50,
                          color: Colors.black.withOpacity(0.10000000149011612),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    )
                  : BoxDecoration(
                      color: isExpandedView
                          ? graphlinecolor2
                          : zoomValue < 5
                              ? kBorderColor.withOpacity(0.12)
                              : graphlinecolor2,
                      gradient: isExpandedView || zoomValue >= 5
                          ? const LinearGradient(
                              colors: [
                                gradientColorOne,
                                gradientColorTwo,
                              ],
                            )
                          : null,
                      borderRadius: BorderRadius.circular(9),
                    ),
              child: SvgPicture.asset(
                Assets.svgPlusIcon,
                color: zoomValue >= 5 ? kBlack : null,
              ),
            ),
          ),
        ),
        // Slider with vertical orientation
        if (context.isDesktop) ...[
          SizedBox(
            height: 100,
            child: RotatedBox(
              quarterTurns: 3,
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  trackHeight: 5,
                  activeTrackColor: gradientColorOne,
                  inactiveTrackColor: zoomValue < 5
                      ? kBorderColor.withOpacity(0.12)
                      : Colors.grey[300],
                  thumbShape: const RoundSliderThumbShape(),
                  overlayShape:
                      const RoundSliderOverlayShape(overlayRadius: 20),
                  overlayColor: graphlinecolor2.withOpacity(0.2),
                  thumbColor: gradientColorOne,
                  valueIndicatorColor: gradientColorOne,
                ),
                child: Slider(
                  value: zoomValue,
                  min: minZoom,
                  max: maxZoom,
                  divisions: (maxZoom - minZoom).toInt(),
                  label: zoomValue.toStringAsFixed(1),
                  onChanged: onChanged,
                ),
              ),
            ),
          ),
          // Minus button to decrease zoom
        ],
        SizedBox(height: context.isMobile ? 5 : 10),
        InkWell(
          onTap: () {
            onChanged(zoomValue - 1);
          },
          child: BlurredBackground(
            borderRadius: 8,
            child: Container(
              height: context.isTablet ? 42 : 32,
              width: context.isTablet ? 42 : 32,
              padding: const EdgeInsets.all(10),
              decoration: zoomValue >= 5
                  ? ShapeDecoration(
                      color: Colors.black.withOpacity(0.0001),
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          width: 0.50,
                          color: Colors.black.withOpacity(0.10000000149011612),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    )
                  : BoxDecoration(
                      color: isExpandedView
                          ? graphlinecolor2
                          : zoomValue < 5
                              ? kBorderColor.withOpacity(0.12)
                              : graphlinecolor2,
                      gradient: isExpandedView || zoomValue >= 5
                          ? const LinearGradient(
                              colors: [
                                gradientColorOne,
                                gradientColorTwo,
                              ],
                            )
                          : null,
                      borderRadius: BorderRadius.circular(9),
                    ),
              child: SvgPicture.asset(
                Assets.svgMinusIcon,
                color: zoomValue >= 5 ? kBlack : null,
              ),
            ),
          ),
        ),
        SizedBox(height: context.isMobile ? 5 : 10),
        InkWell(
          onTap: () {
            onChanged(isExpandedView ? 2 : 1);
          },
          child: BlurredBackground(
            borderRadius: 8,
            child: Container(
              height: context.isTablet ? 42 : 32,
              width: context.isTablet ? 42 : 32,
              padding: const EdgeInsets.all(10),
              decoration: zoomValue >= 5
                  ? ShapeDecoration(
                      color: Colors.black.withOpacity(0.0001),
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          width: 0.50,
                          color: Colors.black.withOpacity(0.10000000149011612),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    )
                  : BoxDecoration(
                      color: isExpandedView
                          ? graphlinecolor2
                          : zoomValue < 5
                              ? kBorderColor.withOpacity(0.12)
                              : graphlinecolor2,
                      borderRadius: BorderRadius.circular(9),
                      gradient: isExpandedView || zoomValue >= 5
                          ? const LinearGradient(
                              colors: [
                                gradientColorOne,
                                gradientColorTwo,
                              ],
                            )
                          : null,
                    ),
              child: SvgPicture.asset(
                Assets.svgRefreshIcon,
                color: zoomValue >= 5 ? kBlack : null,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
