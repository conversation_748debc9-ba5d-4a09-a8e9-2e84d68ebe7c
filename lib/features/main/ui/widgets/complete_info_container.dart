import 'package:flutter/material.dart';
import 'package:nexqloud/core/extensions/responsive_font.dart';

import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/ui/widgets/blurred_background.dart';

class CompleteInfoContainer extends StatelessWidget {
  const CompleteInfoContainer({
    super.key,
    required this.title,
    required this.subtitle,
  });
  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    return BlurredBackground(
      borderRadius: 8,
      // blurColorFilter: Colors.black.withOpacity(0.000100000074505806),
      child: Container(
        width: context.isMobileOrientationWide ? 90 : 120,
        height: context.isMobileOrientationWide ? 45 : 55,
        padding: const EdgeInsets.symmetric(
          horizontal: 6,
          vertical: 4,
        ),
        decoration: ShapeDecoration(
          color: Colors.black.withOpacity(0.0001),
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 0.50,
              color: Colors.black.withOpacity(0.10000000149011612),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Center(
          child: Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: title,
                  style: TextStyle(
                    color: const Color(0xFF1A1143),
                    fontSize: context.responsiveFontSize(14),
                    fontFamily: 'Rubik',
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.28,
                  ),
                ),
                TextSpan(
                  text: '\n',
                  style: TextStyle(
                    color: const Color(0xFF1A1143),
                    fontSize: context.responsiveFontSize(12),
                    fontFamily: 'Rubik',
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.36,
                  ),
                ),
                TextSpan(
                  text: subtitle,
                  style: TextStyle(
                    color: const Color(0xFF626262),
                    fontSize: context.height < 600
                        ? 10
                        : context.responsiveFontSize(12),
                    fontFamily: 'Rubik',
                    fontWeight: FontWeight.w400,
                    letterSpacing: -0.20,
                  ),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
