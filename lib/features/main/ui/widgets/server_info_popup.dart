// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:animate_do/animate_do.dart';
import 'package:flutter/material.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/double.dart';
import 'package:nexqloud/core/extensions/temperature_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/ui/widgets/blurred_background.dart';
import 'package:nexqloud/core/ui/widgets/info_chip.dart';
import 'package:nexqloud/core/ui/widgets/shimmer_loading.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/core/utils/data_size.dart';
import 'package:nexqloud/core/utils/utils.dart';
import 'package:nexqloud/features/main/models/server_info.dart';
import 'package:nexqloud/features/main/providers/server_data_provider.dart';
import 'package:nexqloud/features/main/ui/widgets/gauges_for_modal.dart';
import 'package:nexqloud/features/main/ui/widgets/online_server_indicator.dart';
import 'package:nexqloud/generated/assets.dart';
import 'package:provider/provider.dart';

class ServerInfoPopup extends StatefulWidget {
  const ServerInfoPopup({
    super.key,
    required this.server,
  });
  final ServerInfo server;
  @override
  State<ServerInfoPopup> createState() => _ServerInfoPopupState();
}

class _ServerInfoPopupState extends State<ServerInfoPopup> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        _isLoading = true;
      });
      final provider = context.read<ServerDataProvider>();
      await provider.fetchSingleServerDataViaServerId(widget.server.serverId);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ServerDataProvider>();
    print('Normal Modal Displayed');
    return FadeIn(
      delay: const Duration(milliseconds: 500),
      child: Consumer<ServerDataProvider>(
        builder: (context, _, __) {
          return SizedBox(
            width: 435,
            child: Shimmer(
              linearGradient: shimmerGradient,
              child: ShimmerLoading(
                isLoading: _isLoading,
                child: Stack(
                  children: [
                    SizedBox(
                      child: Image.asset(
                        Assets.pngInfoModalBg,
                        scale: 1.29,
                        fit: BoxFit.contain,
                      ),
                    ),
                    Container(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(18)),
                      ),
                      // margin: const EdgeInsets.only(bottom: 150),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 15,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Space.vertical(10),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Space.horizontal(8),
                                  Text(
                                    provider.fetchedServerData.model ?? '',
                                    style: context.medium?.copyWith(
                                      fontSize: 17,
                                    ),
                                  ),
                                  const Space.horizontal(8),
                                  OnlineServerIndicator(
                                    isOnline:
                                        provider.fetchedServerData.isOnline,
                                  ),
                                ],
                              ),
                              const Spacer(),
                              // const Space.horizontal(125),
                              Container(
                                height: 24,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 6),
                                margin: const EdgeInsets.symmetric(
                                  vertical: 2,
                                  horizontal: 18,
                                ),
                                decoration: BoxDecoration(
                                  color: kWhite,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Colors.black12,
                                      blurRadius: 0.04,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  // mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.location_on_outlined,
                                      color: kBlack,
                                      size: 12,
                                    ),
                                    const Space.horizontal(3),
                                    Text(
                                      provider.fetchedServerData.location.city
                                                  .isEmpty &&
                                              provider.fetchedServerData
                                                  .location.country.isEmpty
                                          ? 'Unknown'
                                          : '${provider.fetchedServerData.location.city}${provider.fetchedServerData.location.city.isNotEmpty && provider.fetchedServerData.location.country.isNotEmpty ? ', ' : ''}${provider.fetchedServerData.location.country}',
                                      style: context.normal!.copyWith(
                                        color: kBlack,
                                        fontSize: 12.5,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const Space.vertical(4),
                          Padding(
                            padding: const EdgeInsets.only(left: 8),
                            child: Text(
                              provider.fetchedServerData.gpu.isNotEmpty
                                  ? '${provider.fetchedServerData.gpu.first.vendor} | ${provider.fetchedServerData.gpu.first.model}'
                                  : 'Integrated Graphics',
                              style: context.normal!.copyWith(
                                fontSize: 12,
                              ),
                            ),
                          ),
                          const Space.vertical(10),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Container(
                              height: 140,
                              width: 384,
                              margin: const EdgeInsets.only(
                                left: 4,
                                right: 12,
                              ),
                              padding: const EdgeInsets.only(right: 10, top: 5),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                image: const DecorationImage(
                                  image: AssetImage(
                                    Assets.pngServerImage,
                                  ),
                                  fit: BoxFit.fill,
                                ),
                              ),
                              alignment: Alignment.topRight,
                              child: BlurredBackground(
                                child: InfoChip(
                                  width: 86,
                                  icon: Assets.svgGreenCircleTickIcon,
                                  value: provider.fetchedServerData.temperature
                                      .toFahrenheitWithSymbol,
                                ),
                              ),
                            ),
                          ),
                          // const Space.vertical(2),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Row(
                              children: [
                                InfoChip(
                                  icon: Assets.svgUptimeClockIcon,
                                  value:
                                      '${provider.fetchedServerData.uptimePercentage.toStringAsFixed(2)}%',
                                ),
                                const Space.horizontal(10),
                                InfoChip(
                                  icon: Assets.svgQloudScoreCloudIcon,
                                  value: provider.fetchedServerData.cloudScore
                                      .toPercentage,
                                ),
                                const Space.horizontal(10),
                                InfoChip(
                                  icon: Assets.svgLicenseKeyIcon,
                                  value:
                                      provider.fetchedServerData.nftTokenId ??
                                          '',
                                ),
                              ],
                            ),
                          ),
                          const Space.vertical(10),
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 8),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CircularGaugeForModal(
                                  value: provider
                                      .fetchedServerData.networkInbound
                                      .toStringAsFixed(2),
                                  progress: provider.fetchedServerData
                                      .percentages.networkInboundPercentile,
                                  unit: 'GBit/s',
                                  title: 'Network',
                                  // subCardTitle: 'Devices Online',
                                  // subCardValue: '65',
                                ),
                                const Space.horizontal(6),
                                SizedBox(
                                  child: NeedleCircularGaugeForModal(
                                    title: '',
                                    progress: provider.fetchedServerData
                                        .percentages.cpuUtilizationPercentile,
                                    unit:
                                        '${provider.fetchedServerData.cpu.toStringAsFixed(2)}%',
                                    value: 'CPU',
                                    subCardTitle: '27.5k/58.7k',
                                    subCardValue:
                                        '${provider.fetchedServerData.percentages.cpuUtilizationPercentile.toStringAsFixed(2)}%',
                                  ),
                                ),
                                const Space.horizontal(6),
                                SizedBox(
                                  child: NeedleCircularGaugeForModal(
                                    title: '',
                                    progress: provider.fetchedServerData
                                        .percentages.ramUtilizationPercentile,
                                    unit:
                                        '${provider.fetchedServerData.percentages.ramUtilizationPercentile.toStringAsFixed(2)}%',
                                    value: 'RAM',
                                    subCardTitle: '27.5k/58.7k',
                                    subCardValue:
                                        '${provider.fetchedServerData.percentages.ramUtilizationPercentile.toStringAsFixed(2)}%',
                                  ),
                                ),
                                const Space.horizontal(6),
                                CircularGaugeForModal(
                                  value: formatBytes(
                                    adjustBytes(
                                      provider.fetchedServerData.diskRead,
                                    ),
                                    showUnits: false,
                                  ),
                                  progress: provider.fetchedServerData
                                      .percentages.diskReadPercentile,
                                  unit: dataSizeUnit(
                                    adjustBytes(
                                      provider.fetchedServerData.diskRead,
                                    ),
                                  ),
                                  title: 'Disk',
                                  // subCardTitle: 'Devices Online',
                                  // subCardValue: '65',
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
