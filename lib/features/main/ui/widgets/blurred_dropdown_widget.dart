import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/core/utils/utils.dart';
import 'package:nexqloud/generated/assets.dart';

class BlurredDropdownButton extends StatefulWidget {
  BlurredDropdownButton({
    super.key,
    required this.items,
    required this.value,
    required this.onChanged,
    this.isRegion = false,
    this.customWidth,
    this.statusIconWidget,
  });
  final List<String> items;
  final String value;
  final ValueChanged<String?> onChanged;
  final bool isRegion;
  double? customWidth;
  Widget? statusIconWidget;

  @override
  BlurredDropdownButtonState createState() => BlurredDropdownButtonState();
}

class BlurredDropdownButtonState extends State<BlurredDropdownButton> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool isDropdownOpen = false;
  late ScrollController _scrollController; // Added ScrollController

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController(); // Initialize the controller
  }

  @override
  void dispose() {
    _scrollController.dispose(); // Dispose the controller
    super.dispose();
  }

  void _toggleDropdown() {
    if (isDropdownOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _openDropdown() {
    final renderBox = context.findRenderObject()! as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) {
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: _closeDropdown,
          child: Stack(
            children: [
              Positioned(
                width: size.width,
                left: offset.dx,
                top: offset.dy + size.height,
                child: CompositedTransformFollower(
                  link: _layerLink,
                  showWhenUnlinked: false,
                  child: Material(
                    color: Colors.transparent,
                    child: ClipRRect(
                      borderRadius:
                          BorderRadius.circular(8), // Ensure consistent radius
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black54.withOpacity(0.1),
                            borderRadius:
                                BorderRadius.circular(8), // Match radius here
                            border: Border.all(
                              color: Colors.white.withOpacity(0.2),
                            ),
                          ),
                          constraints: const BoxConstraints(
                            maxHeight: 48.0 * 10, // Adjust based on item height
                          ),
                          child: ListView(
                            controller: _scrollController,
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            children: widget.items.map((item) {
                              final isSelected = item == widget.value;
                              return Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 2.0),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                      8), // Match parent radius
                                  child: Ink(
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? Colors.white.withOpacity(0.2)
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(
                                          8), // Match radius
                                    ),
                                    child: ListTile(
                                      title: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            widget.isRegion
                                                ? item == 'All'
                                                    ? '$item Regions'
                                                    : getRegionName(item,
                                                        isShort: true)
                                                : item == 'All'
                                                    ? '$item Devices'
                                                    : item,
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: isSelected
                                                  ? FontWeight.bold
                                                  : FontWeight.normal,
                                            ),
                                          ),
                                          if (widget.isRegion &&
                                              item != 'All' &&
                                              item != 'Unknown')
                                            Text(
                                              item,
                                              style: TextStyle(
                                                color: kWhite.withOpacity(0.5),
                                                fontWeight: isSelected
                                                    ? FontWeight.bold
                                                    : FontWeight.normal,
                                                fontSize: 13,
                                              ),
                                            ),
                                        ],
                                      ),
                                      onTap: () {
                                        widget.onChanged(item);
                                        _closeDropdown();
                                      },
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      isDropdownOpen = true;
    });

    // Scroll to the selected item
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final index = widget.items.indexOf(widget.value);
      if (index != -1) {
        const itemHeight = 48.0; // Adjust if your item height is different
        var offset =
            itemHeight * index - (itemHeight * 2); // Center the selected item
        if (offset < 0) offset = 0.0;
        if (offset > _scrollController.position.maxScrollExtent) {
          offset = _scrollController.position.maxScrollExtent;
        }
        _scrollController.animateTo(
          offset,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _closeDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      isDropdownOpen = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleDropdown,
      child: CompositedTransformTarget(
        link: _layerLink,
        child: Container(
          height: 49,
          width: widget.customWidth ?? MediaQuery.of(context).size.width * 0.13,
          padding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 10,
          ),
          decoration: BoxDecoration(
            color: Color(0xFFA2DCFF).withOpacity(0.09),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: kBlack.withOpacity(0.03),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
            border: GradientBoxBorder(
              gradient: LinearGradient(
                colors: [
                  kWhite.withOpacity(0.15),
                  kWhite.withOpacity(0.15),
                  // gradientColorTwo.withOpacity(0.1),
                  // kWhite.withOpacity(0.02),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          child: Row(
            children: [
              if (widget.isRegion) ...[
                SvgPicture.asset(
                  Assets.svgPinLocationWhite,
                  height: 24,
                ),
                const Space.horizontal(5),
              ] else ...[
                widget.statusIconWidget ?? const SizedBox(),
              ],
              Text(
                widget.isRegion
                    ? widget.value == 'All'
                        ? '${widget.value} Regions'
                        : getRegionName(widget.value, isShort: true)
                    : widget.value == 'All'
                        ? '${widget.value} Devices'
                        : widget.value,
                style: const TextStyle(color: Colors.white),
              ),
              const Spacer(),
              SvgPicture.asset(
                'assets/icons/svg/drop_down_arrow_down.svg',
                height: 18,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
