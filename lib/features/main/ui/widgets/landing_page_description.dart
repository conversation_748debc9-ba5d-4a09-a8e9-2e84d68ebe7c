import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';

import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/constants/constants.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/features/main/ui/widgets/app_store_icon.dart';
import 'package:nexqloud/features/main/utils/url_launcher.dart';

class LandingPageDescription extends StatelessWidget {
  const LandingPageDescription({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: context.isDesktop ? 470 : context.sizeRatio * 0.28,
      width: context.isDesktop || context.isDesktopLarge
          ? context.horizontalPadding
          : null,
      margin: const EdgeInsets.only(
        bottom: 64,
        top: 20,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          context.isDesktop
              ? 50
              : context.isMobile
                  ? 20
                  : 30,
        ),
        gradient: const LinearGradient(
          colors: [
            gradientColorThree,
            gradientColorTwo,
            gradientColorOne,
          ],
        ),
      ),
      child: Stack(
        children: [
          Positioned(
            right: context.isDesktop ? -210 : -25,
            top: 20,
            child: SvgPicture.network(
              'https://cdn.prod.website-files.com/655820c4653a34517d9859b4/655820c4653a34517d9859da_Vector(13).svg',
              height: context.isDesktop ? null : 150,
              width: context.isDesktop ? null : 150,
            ),
          ),
          Positioned(
            bottom: context.isDesktop ? 20 : 10,
            left: context.isDesktop ? -210 : 10,
            child: SvgPicture.network(
              'https://cdn.prod.website-files.com/655820c4653a34517d9859b4/655820c4653a34517d9859da_Vector(13).svg',
              height: context.isDesktop ? null : 150,
              width: context.isDesktop ? null : 150,
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                context.isDesktop
                    ? "POWER TOMORROW'S CLOUD\nTODAY WITH NEXQLOUD"
                    : "POWER TOMORROW'S\nCLOUD TODAY WITH\nNEXQLOUD",
                style: context.medium?.copyWith(
                  fontSize: context.isMobile || context.isTablet
                      ? context.sizeRatio * 0.024
                      : 40,
                ),
                textAlign: TextAlign.center,
              ),
              const Space.vertical(18),
              Text(
                context.isDesktop
                    ? 'Start earning, saving, and innovating with our\ncollaborative ecosystem today.'
                    : 'Start earning, saving, and innovating\nwith our collaborative ecosystem today.',
                style: context.normal?.copyWith(
                  fontSize: context.isMobile || context.isTablet
                      ? context.sizeRatio * 0.012
                      : 18,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const Space.vertical(20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppStoreIcon(
                    assetPath: 'assets/icons/svg/apple_icon_white.svg',
                    onTap: () => UrlLauncher.openUrl(
                      kAppStoreUrl,
                    ),
                  ),
                  const Space.horizontal(10),
                  AppStoreIcon(
                    assetPath: 'assets/icons/svg/google_play_icon_white.svg',
                    onTap: () => UrlLauncher.openUrl(
                      kPlayStoreUrl,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
