import 'package:flutter/material.dart';

import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';

class AnalyticsLinearProgressView extends StatelessWidget {
  const AnalyticsLinearProgressView({
    super.key,
    required this.title,
    required this.value,
    required this.unit,
    required this.progress,
  });
  final String title;
  final String value;
  final String unit;
  final double progress;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: context.isMobile || context.isTablet ? 8 : 20,
        vertical: context.isMobile || context.isTablet ? 8 : 12,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Builder(
                  builder: (context) {
                    final height = MediaQuery.of(context).size.height;
                    final width = MediaQuery.of(context).size.width;
                    final scaledHeight = height * 14 / height;
                    final scaledShadeHeight = height * 10 / height;
                    return Stack(
                      children: [
                        Container(
                          width: width,
                          height: scaledHeight,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            color: Colors.white.withOpacity(
                              0.08,
                            ), // Changed to a lighter white color
                          ),
                        ),
                        Container(
                          width: width,
                          height: scaledShadeHeight,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: <Color>[
                                Colors.black.withOpacity(
                                  0.25,
                                ),
                                Colors.black.withOpacity(
                                  0.15,
                                ),
                                Colors.black.withOpacity(
                                  0.08,
                                ),
                                kTransparent,
                              ],
                              // stops: const <double>[0.25, 0.75],
                            ),
                          ),
                        ),
                        Container(
                          width:
                              ((285 / 360) * ((progress * 100) / 100)) * width,
                          height: scaledHeight,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            gradient: const LinearGradient(
                              colors: <Color>[
                                Color(0xFF9933FF),
                                Color(0xFF33B1FF),
                              ],
                              stops: <double>[0.25, 0.75],
                            ),
                          ),
                        ),
                        Container(
                          width:
                              ((285 / 360) * ((progress * 100) / 100)) * width,
                          height: scaledHeight,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            gradient: LinearGradient(
                              colors: <Color>[
                                kTransparent,
                                kWhite.withOpacity(0.02), // Lowest opacity
                                kWhite.withOpacity(0.02),
                                kWhite.withOpacity(0.02),
                                kWhite.withOpacity(0.02),
                                kWhite.withOpacity(0.02),
                                kWhite.withOpacity(0.02),
                                kWhite.withOpacity(0.01),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
          const Space.vertical(8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: value,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontFamily: 'Livvic',
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: ' $unit',
                      style: const TextStyle(
                        color: Color(0xFFABABAB),
                        fontSize: 14,
                        fontFamily: 'Rubik',
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                title,
                textAlign: TextAlign.right,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.5),
                  fontSize: 16,
                  fontFamily: 'Rubik',
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
