import 'package:flutter/cupertino.dart';

class DropNotchPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0XFF403A8A)
      ..style = PaintingStyle.fill;

    final path = Path();
    const notchWidth = 43;
    const notchHeight = 10;
    const notchCurve = 8;

    // Start from the top-left corner
    path.moveTo(2, 2);

    // Draw left, bottom, and right edges
    path.lineTo(0, size.height);
    path.lineTo((size.width - notchWidth) / 2, size.height);

    // Draw the notch
    path.cubicTo(
      (size.width - notchWidth) / 2 + notchCurve,
      size.height,
      size.width / 2 - notchWidth / 4,
      size.height + notchHeight,
      size.width / 2,
      size.height + notchHeight,
    );
    path.cubicTo(
      size.width / 2 + notchWidth / 4,
      size.height + notchHeight,
      (size.width + notchWidth) / 2 - notchCurve,
      size.height,
      (size.width + notchWidth) / 2,
      size.height,
    );

    // Draw remaining bottom and right edges
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 0);

    // Close path
    path.close();

    // Draw the path
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
