import 'package:flutter/material.dart';

import 'package:animate_do/animate_do.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_maps/maps.dart';

import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/log.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/features/main/models/server_model.dart';
import 'package:nexqloud/features/main/providers/server_data_provider.dart';
import 'package:nexqloud/features/main/ui/widgets/vertical_zoom_slider.dart';

class ContinentMap extends StatefulWidget {
  const ContinentMap({
    super.key,
    required this.continent,
    required this.filePath,
    this.serverModel,
    this.selectedMarkerIndex = -1,
    required this.isSelectedAMarker,
  });
  final String continent;
  final String filePath;
  final ServerModel? serverModel;
  final int selectedMarkerIndex;
  final bool isSelectedAMarker;
  @override
  State<ContinentMap> createState() => _ContinentMapState();
}

class _ContinentMapState extends State<ContinentMap>
    with SingleTickerProviderStateMixin {
  final List<ServerModel> _continentMarkersData = [];
  late MapShapeSource _continentDataSource;
  late MapZoomPanBehavior _continentMapZoomPanBehavior;
  late MapShapeLayerController _continentMapShapeController;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  bool isLoading = false;

  late ServerModel _selectedMarker;
  double _worldMapZoomLevel = 1;
  int _hoveredMarkerIndex = -1;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..repeat(reverse: true);

    // Define the animation scaling factor
    _pulseAnimation = Tween<double>(begin: 1, end: 1.3).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _continentMapShapeController = MapShapeLayerController();

    _configureContinentMap();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? const Center(
            child: CircularProgressIndicator(
              color: gradientColorThree,
            ),
          )
        : IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      border: GradientBoxBorder(
                        gradient: LinearGradient(
                          colors: [
                            kWhite.withOpacity(0.2),
                            kWhite.withOpacity(0.1),
                            const Color(0xFF4e65b3).withOpacity(0.5),
                            kWhite.withOpacity(0.1),
                            kWhite.withOpacity(0.02),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildDataRowCard(
                            fieldName: 'Region',
                            value: _selectedMarker.region,
                          ),
                          // _buildDataRowCard(
                          //   fieldName: 'Address',
                          //   value: '14th Street,Downtown',
                          // ),
                          const Space.vertical(14),
                          _buildDataRowCard(
                            fieldName: 'Processor Type:',
                            value: 'Intel Core i7-13700T',
                          ),
                          const Space.vertical(14),
                          _buildDataRowCard(
                            fieldName: 'Cores:',
                            value: '16',
                          ),
                          const Space.vertical(14),
                          _buildDataRowCard(
                            fieldName: 'Maximum Speed:',
                            value: '5.2 GHz (single-core boost',
                          ),
                          const Space.vertical(14),
                          _buildDataRowCard(
                            fieldName: 'Logical Processors:',
                            value: '24 Threads (16 cores)',
                          ),
                          const Space.vertical(14),
                          _buildDataRowCard(
                            fieldName: 'L3 Cache:',
                            value: 'US, UK',
                          ),
                          const Space.vertical(14),
                          _buildDataRowCard(
                            fieldName: 'Qloud Score:',
                            value: '78',
                          ),
                          const Space.vertical(14),
                          _buildDataRowCard(
                            fieldName: 'Memory Type:',
                            value: '128 GB DDR5',
                          ),
                          const Space.vertical(14),
                          _buildDataRowCard(
                            fieldName: 'Disk Type:',
                            value: '512 GB SSD',
                          ),
                          const Space.vertical(14),
                          _buildDataRowCard(
                            fieldName: 'Uptime:',
                            value: '${_selectedMarker.uptime}%',
                          ),
                          const Space.vertical(14),
                          _buildDataRowCard(
                            fieldName: 'License:',
                            value: '452687',
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const Space.horizontal(14),
                Expanded(
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      border: GradientBoxBorder(
                        gradient: LinearGradient(
                          colors: [
                            kWhite.withOpacity(0.2),
                            kWhite.withOpacity(0.1),
                            const Color(0xFF4e65b3).withOpacity(0.5),
                            kWhite.withOpacity(0.1),
                            kWhite.withOpacity(0.02),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: Center(
                            child: SfMapsTheme(
                              data: SfMapsThemeData(
                                shapeHoverColor: kTransparent,
                                shapeHoverStrokeColor: kWhite.withOpacity(0.22),
                                tooltipColor: kWhite.withOpacity(0.2),
                                tooltipStrokeColor: kWhite.withOpacity(0.1),
                                tooltipBorderRadius: BorderRadius.circular(15),
                              ),
                              child: Center(
                                child: SfMaps(
                                  layers: <MapLayer>[
                                    MapShapeLayer(
                                      strokeWidth: 0,
                                      dataLabelSettings:
                                          const MapDataLabelSettings(
                                        textStyle: TextStyle(
                                          color: kWhite,
                                          fontSize: 7.5,
                                        ),
                                      ),
                                      source: _continentDataSource,
                                      initialMarkersCount:
                                          _continentMarkersData.length,
                                      color: kWhite.withOpacity(0.2),
                                      strokeColor: kWhite.withOpacity(0.22),
                                      controller: _continentMapShapeController,
                                      zoomPanBehavior:
                                          _continentMapZoomPanBehavior,
                                      markerBuilder: (context, index) {
                                        double markerSize;

                                        if (_continentMarkersData.length <=
                                            10) {
                                          markerSize = 18.0;
                                        } else if (_continentMarkersData
                                                .length <=
                                            50) {
                                          markerSize = 15.0;
                                        } else {
                                          markerSize = 10.0;
                                        }
                                        return MapMarker(
                                          latitude: _continentMarkersData[index]
                                              .latitude,
                                          longitude:
                                              _continentMarkersData[index]
                                                  .longitude,
                                          size: Size(markerSize, markerSize),
                                          iconColor:
                                              graphlinecolor2.withOpacity(0.8),
                                          child: MouseRegion(
                                            onHover: (event) {
                                              'hovered'.printInfo();
                                              setState(() {
                                                _hoveredMarkerIndex = index;
                                              });
                                              _continentMapShapeController
                                                  .updateMarkers([index]);
                                            },
                                            onExit: (event) {
                                              'exited'.printInfo();
                                              setState(() {
                                                _hoveredMarkerIndex = -1;
                                              });
                                              _continentMapShapeController
                                                  .updateMarkers([index]);
                                            },
                                            child: ZoomIn(
                                              child: GestureDetector(
                                                onTap: () {
                                                  final markerData =
                                                      _continentMarkersData[
                                                          index];
                                                  _onMarkerSelect(markerData);
                                                  _continentMapShapeController
                                                      .updateMarkers([index]);
                                                },
                                                child: AnimatedBuilder(
                                                  animation: _pulseController,
                                                  builder: (context, child) {
                                                    return Transform.scale(
                                                      scale: _selectedMarker ==
                                                              _continentMarkersData[
                                                                  index]
                                                          ? _pulseAnimation
                                                              .value
                                                          : 1.0,
                                                      child: Container(
                                                        height: markerSize,
                                                        width: markerSize,
                                                        decoration:
                                                            BoxDecoration(
                                                          shape:
                                                              BoxShape.circle,
                                                          gradient: _selectedMarker ==
                                                                      _continentMarkersData[
                                                                          index] ||
                                                                  _hoveredMarkerIndex ==
                                                                      index
                                                              ? const LinearGradient(
                                                                  colors: [
                                                                    gradientColorTwo,
                                                                    gradientColorOne,
                                                                  ],
                                                                  begin: Alignment
                                                                      .topCenter,
                                                                  end: Alignment
                                                                      .bottomCenter,
                                                                )
                                                              : null,
                                                          border:
                                                              _hoveredMarkerIndex ==
                                                                      index
                                                                  ? Border.all(
                                                                      color:
                                                                          const Color(
                                                                        0xFFAF92FF,
                                                                      ),
                                                                    )
                                                                  : null,
                                                          boxShadow: _selectedMarker ==
                                                                      _continentMarkersData[
                                                                          index] ||
                                                                  _hoveredMarkerIndex ==
                                                                      index
                                                              ? [
                                                                  BoxShadow(
                                                                    blurRadius:
                                                                        15,
                                                                    spreadRadius:
                                                                        2,
                                                                    color: gradientColorOne
                                                                        .withOpacity(
                                                                      0.5,
                                                                    ),
                                                                  ),
                                                                ]
                                                              : null,
                                                          color: graphlinecolor2
                                                              .withOpacity(0.8),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: VerticalZoomSlider(
                            zoomValue: _worldMapZoomLevel,
                            onChanged: (value) {
                              setState(() {
                                if (value > 0 && value < 10) {
                                  _worldMapZoomLevel = value;

                                  _continentMapZoomPanBehavior.zoomLevel =
                                      value;
                                }
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
  }

  Row _buildDataRowCard({
    required String fieldName,
    required String value,
    String? licenseImg,
  }) {
    return Row(
      children: [
        SizedBox(
          width: context.width * 0.12,
          child: Text(
            fieldName,
            style: context.bold?.copyWith(
              fontSize: 15,
            ),
          ),
        ),
        if (licenseImg != null) ...[
          Image.asset(licenseImg),
          const Space.horizontal(8),
        ],
        Text(
          value,
          style: context.normal?.copyWith(
            fontSize: 15,
            color: const Color(0xFFD9D9D9),
          ),
        ),
      ],
    );
  }

  void _initializeSelectedMarker() {
    // setState(() {
    //   if (widget.serverModel != null) {
    //     _selectedMarker = widget.serverModel!;
    //   } else {
    //     _selectedMarker = _continentMarkersData.isEmpty
    //         ? const ServerModel(
    //             serverName: 'SRV-84O70Q',
    //             uptime: 99.87,
    //             region: 'US-E1',
    //             cores: 32,
    //             memory: 64,
    //             country: 'United States',
    //             latitude: 37.7749,
    //             longitude: -122.4194,
    //             continent: 'North America',
    //           )
    //         : _continentMarkersData.first;
    //   }
    //   _onMarkerSelect(_selectedMarker);
    // });
  }

  void _onMarkerSelect(ServerModel markerData) {
    setState(() {
      _selectedMarker = markerData;
      // Selected marker: $_selectedMarker
    });
  }

  Future<void> _configureContinentMap() async {
    try {
      setState(() {
        isLoading = true;
      });

      // final countries =
      //     context.read<ServerDataProvider>().findCountryInContinent(
      //           widget.continent,
      //         );
      // for (final country in countries) {
      //   final serversInACountry =
      //       context.read<ServerDataProvider>().findCountry(country);
      //   _continentMarkersData.addAll(serversInACountry);
      // }

      _continentMapShapeController.clearMarkers();
      for (var i = 0; i < _continentMarkersData.length; i++) {
        _continentMapShapeController.insertMarker(i);
      }

      _continentDataSource = MapShapeSource.asset(
        widget.filePath,
        shapeDataField: 'name',
        dataCount: _continentMarkersData.length,
        primaryValueMapper: (index) => _continentMarkersData[index].country,
      );
      _initializeSelectedMarker();

      if (widget.isSelectedAMarker) {
        _worldMapZoomLevel = 2;
      } else {
        _worldMapZoomLevel = 1;
      }
      _continentMapZoomPanBehavior = MapZoomPanBehavior(
        enableDoubleTapZooming: true,
        zoomLevel: _worldMapZoomLevel,
        focalLatLng: MapLatLng(
          _selectedMarker.latitude,
          _selectedMarker.longitude,
        ),
        showToolbar: false,
        toolbarSettings: const MapToolbarSettings(
          itemBackgroundColor: graphlinecolor2,
          iconColor: kWhite,
          itemHoverColor: kPurpleColor,
          direction: Axis.vertical,
          position: MapToolbarPosition.bottomRight,
        ),
      );

      await Future.delayed(const Duration(seconds: 1));
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      e.printError();
    }
  }
}
