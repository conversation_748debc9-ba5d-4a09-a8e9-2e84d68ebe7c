import 'package:flutter/cupertino.dart';

import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';

class CustomFooterButtons extends StatelessWidget {
  const CustomFooterButtons({
    required this.title,
    required this.buttons,
    required this.onTapFunctions,
    super.key,
  }) : assert(
          buttons.length == onTapFunctions.length,
          'Buttons and onTapFunctions must have the same length',
        );
  final String title;
  final List<String> buttons;
  final List<VoidCallback> onTapFunctions;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: context.medium?.copyWith(fontSize: 18),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 25),
        ...List.generate(buttons.length, (index) {
          return Column(
            children: [
              HoverCupertinoButton(
                onPressed: onTapFunctions[index],
                hoverColor: kPurpleColor,
                normalColor: kFooterButtonColor,
                child: Text(
                  buttons[index],
                  style: context.normal?.copyWith(
                    fontSize: context.isDesktop ? 16 : 16,
                    color: kFooterButtonColor,
                  ),
                ),
              ),
              const SizedBox(height: 25),
            ],
          );
        }),
      ],
    );
  }
}

class HoverCupertinoButton extends StatefulWidget {
  const HoverCupertinoButton({
    super.key,
    required this.onPressed,
    required this.child,
    required this.hoverColor,
    required this.normalColor,
    this.padding = EdgeInsets.zero,
    this.minSize = 0,
  });
  final VoidCallback onPressed;
  final EdgeInsetsGeometry padding;
  final double minSize;
  final Text child;
  final Color hoverColor;
  final Color normalColor;

  @override
  HoverCupertinoButtonState createState() => HoverCupertinoButtonState();
}

class HoverCupertinoButtonState extends State<HoverCupertinoButton> {
  bool _isHovering = false;

  void _onHover(bool hovering) {
    setState(() {
      _isHovering = hovering;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: CupertinoButton(
        onPressed: widget.onPressed,
        padding: widget.padding,
        minSize: widget.minSize,
        child: Text(
          widget.child.data ?? '',
          style: widget.child.style?.copyWith(
            color: _isHovering ? widget.hoverColor : widget.normalColor,
          ),
        ),
      ),
    );
  }
}
