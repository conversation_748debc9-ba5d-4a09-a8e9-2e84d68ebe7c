import 'package:animate_do/animate_do.dart';
import 'package:flutter/cupertino.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/features/main/ui/widgets/glow_arc_painter.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

class CircularGaugeForModal extends StatelessWidget {
  const CircularGaugeForModal({
    super.key,
    required this.value,
    required this.progress,
    required this.unit,
    required this.title,
    this.subCardTitle = '',
    this.subCardValue = '',
  });
  final String value;
  final double progress;
  final String unit;
  final String title;
  final String subCardTitle;
  final String subCardValue;
  // final width

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: context.isDesktop ? 105 : 115,
          width: context.isDesktop ? 89 : null,
          // padding: const EdgeInsets.only(bottom: 26),
          decoration: BoxDecoration(
            color: kBorderColor.withOpacity(0.12),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: kWhite.withOpacity(0.2), width: 0.5),
          ),
          child: Container(
            margin: EdgeInsets.symmetric(
              vertical: context.isDesktop ? 0 : 0,
            ),
            padding: const EdgeInsets.only(bottom: 10),
            child: SfRadialGauge(
              axes: <RadialAxis>[
                RadialAxis(
                  startAngle: -90,
                  endAngle: 270,
                  radiusFactor: 0.75,
                  showFirstLabel: false,
                  showLabels: false,
                  showTicks: false,
                  annotations: [
                    GaugeAnnotation(
                      angle: 0, // Centered
                      widget: FadeIn(
                        delay: const Duration(milliseconds: 1700),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            final size = constraints.maxWidth;

                            return SizedBox(
                              width: size - 5,
                              height: size - 5,
                              child: CustomPaint(
                                painter: GlowArcPainterCircular(
                                  progress: progress,
                                  glowColor: const Color(
                                    0xFF33B1FF,
                                  ).withValues(alpha: 0.5), // Your glow color
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    GaugeAnnotation(
                      widget: Stack(
                        children: [
                          Align(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  value, //'4.126'
                                  style: context.bold?.copyWith(
                                    fontSize: 12,
                                  ),
                                ),
                                Text(
                                  unit, //'GBit/s'
                                  style: context.normal?.copyWith(
                                    fontSize: 10,
                                    color: const Color(0xFFBFBFBF),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  axisLineStyle: AxisLineStyle(
                    thickness: 0.2,
                    color: kWhite.withOpacity(0.9),
                    // color: const Color(0xFFEEEEEE).withOpacity(0.14),
                    thicknessUnit: GaugeSizeUnit.factor,
                  ),
                  pointers: <GaugePointer>[
                    // Background RangePointers to create depth effect
                    RangePointer(
                      value: 100, // Full circle
                      width: 0.02,
                      sizeUnit: GaugeSizeUnit.factor,
                      color: kBlack.withOpacity(0.075), // Edge color
                      // cornerStyle: CornerStyle.bothCurve,
                    ),
                    RangePointer(
                      value: 100,
                      width: 0.05,
                      sizeUnit: GaugeSizeUnit.factor,
                      color: kBlack.withOpacity(0.065),
                      // cornerStyle: CornerStyle.bothCurve,
                    ),
                    RangePointer(
                      value: 100,
                      width: 0.07,
                      sizeUnit: GaugeSizeUnit.factor,
                      color: kBlack.withOpacity(0.04),
                      // cornerStyle: CornerStyle.bothCurve,
                    ),
                    RangePointer(
                      value: 100,
                      width: 0.09,
                      sizeUnit: GaugeSizeUnit.factor,
                      color: kBlack.withOpacity(0.02),
                      // cornerStyle: CornerStyle.bothCurve,
                    ),

                    ///Main ProgressPointer
                    RangePointer(
                      enableAnimation: true,
                      animationDuration: 1600,
                      value: progress, // 40
                      width: 0.2,
                      sizeUnit: GaugeSizeUnit.factor,
                      cornerStyle: CornerStyle.bothCurve,
                      gradient: getDynamicGaugeGradient(progress),
                    ),
                    // Background RangePointers to create depth effect
                    RangePointer(
                      value: progress - 1,
                      width: 0.09,
                      sizeUnit: GaugeSizeUnit.factor,
                      gradient: SweepGradient(
                        colors: <Color>[
                          kTransparent,
                          kWhite.withOpacity(0.02), // Lowest opacity
                          kWhite.withOpacity(0.02),
                          kWhite.withOpacity(0.02),
                          kWhite.withOpacity(0.02),
                        ],
                      ),
                      cornerStyle: CornerStyle.bothCurve,
                      enableAnimation: true,
                    ),

                    RangePointer(
                      value: progress - 1,
                      width: 0.07,
                      sizeUnit: GaugeSizeUnit.factor,
                      gradient: SweepGradient(
                        colors: <Color>[
                          kTransparent,
                          kWhite.withOpacity(0.04),
                          kWhite.withOpacity(0.04),
                          kWhite.withOpacity(0.04),
                          kWhite.withOpacity(0.04),
                        ],
                      ),
                      cornerStyle: CornerStyle.bothCurve,
                      enableAnimation: true,
                    ),

                    RangePointer(
                      value: progress - 1,
                      width: 0.05,
                      sizeUnit: GaugeSizeUnit.factor,
                      gradient: SweepGradient(
                        colors: <Color>[
                          kTransparent,
                          kWhite.withOpacity(0.065),
                          kWhite.withOpacity(0.065),
                          kWhite.withOpacity(0.065),
                          kWhite.withOpacity(0.065),
                        ],
                      ),
                      cornerStyle: CornerStyle.bothCurve,
                      enableAnimation: true,
                    ),

                    RangePointer(
                      value: progress - 1,
                      width: 0.02,
                      sizeUnit: GaugeSizeUnit.factor,
                      gradient: SweepGradient(
                        colors: <Color>[
                          kTransparent,
                          kWhite.withOpacity(0.075), // Highest opacity
                          kWhite.withOpacity(0.075),
                          kWhite.withOpacity(0.075),
                          kWhite.withOpacity(0.075),
                        ],
                      ),
                      cornerStyle: CornerStyle.bothCurve,
                      enableAnimation: true,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: 4,
          left: 0,
          right: 0,
          child: Container(
            alignment: Alignment.center,
            // margin: const EdgeInsets.symmetric(
            //   horizontal: 12,
            // ),
            child: Text(
              title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: context.normal?.copyWith(
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class NeedleCircularGaugeForModal extends StatelessWidget {
  const NeedleCircularGaugeForModal({
    super.key,
    required this.value,
    required this.progress,
    required this.unit,
    required this.title,
    required this.subCardTitle,
    required this.subCardValue,
  });
  final String value;
  final double progress;
  final String unit;
  final String title;
  final String subCardTitle;
  final String subCardValue;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: context.isDesktop ? 105 : 115,
      width: context.isDesktop ? 89 : null,
      // padding: const EdgeInsets.only(bottom: 26),
      decoration: BoxDecoration(
        color: kBorderColor.withOpacity(0.12),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: kWhite.withOpacity(0.2), width: 0.5),
      ),
      child: Container(
        margin: EdgeInsets.symmetric(
          vertical: context.isDesktop ? 0 : 0,
        ),
        padding: const EdgeInsets.only(bottom: 10),
        child: SfRadialGauge(
          axes: <RadialAxis>[
            RadialAxis(
              radiusFactor: 0.85,
              showFirstLabel: false,
              showLabels: false,
              showTicks: false,
              annotations: [
                GaugeAnnotation(
                  angle: 90,
                  positionFactor: context.isTablet
                      ? 0.7
                      : context.isMobile
                          ? 0.7
                          : 0.67,
                  widget: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Space.vertical(context.isDesktopExtraSmall ? 25 : 35),
                      Text(
                        value, //'4.126'
                        style: context.normal?.copyWith(
                          fontSize: 12,
                          color: kWhite,
                        ),
                      ),
                      const Space.vertical(1),
                      Text(
                        unit, //'GBit/s'
                        overflow: TextOverflow.ellipsis,
                        style: context.normal?.copyWith(
                          fontSize: 14,
                          color: kWhite,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Space.vertical(6),
                    ],
                  ),
                ),
                GaugeAnnotation(
                  angle: 0, // Centered
                  widget: FadeIn(
                    delay: const Duration(milliseconds: 1700),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        final size = constraints.maxWidth;

                        return SizedBox(
                          width: size / 2,
                          height: size / 2,
                          child: CustomPaint(
                            painter: GlowArcPainterHalfGauge(
                              progress: progress,
                              glowColor: const Color(
                                0xFF33B1FF,
                              ), // Your glow color
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
              axisLineStyle: AxisLineStyle(
                thickness: 0.2,
                color: kWhite.withOpacity(0.9),
                // color: const Color(0xFFEEEEEE).withOpacity(0.14),
                thicknessUnit: GaugeSizeUnit.factor,
                cornerStyle: CornerStyle.bothCurve,
              ),
              pointers: <GaugePointer>[
                RangePointer(
                  value: 99, // Full circle
                  width: 0.02,
                  sizeUnit: GaugeSizeUnit.factor,
                  color: kBlack.withOpacity(0.075), // Edge color
                  cornerStyle: CornerStyle.bothCurve,
                ),
                RangePointer(
                  value: 99,
                  width: 0.05,
                  sizeUnit: GaugeSizeUnit.factor,
                  color: kBlack.withOpacity(0.065),
                  cornerStyle: CornerStyle.bothCurve,
                ),
                RangePointer(
                  value: 99,
                  width: 0.07,
                  sizeUnit: GaugeSizeUnit.factor,
                  color: kBlack.withOpacity(0.04),
                  cornerStyle: CornerStyle.bothCurve,
                ),
                RangePointer(
                  value: 99,
                  width: 0.09,
                  sizeUnit: GaugeSizeUnit.factor,
                  color: kBlack.withOpacity(0.02),
                  cornerStyle: CornerStyle.bothCurve,
                ),
                NeedlePointer(
                  enableAnimation: true,
                  animationDuration: 1600,
                  value: progress, // 40
                  needleColor: const Color(0xFF8052FF),
                  needleEndWidth: 1.2,
                  needleStartWidth: 0,
                  knobStyle: const KnobStyle(
                    color: kWhite,
                  ),
                ),
                RangePointer(
                  enableAnimation: true,
                  animationDuration: 1600,
                  value: progress, // 40
                  width: 0.2,
                  sizeUnit: GaugeSizeUnit.factor,
                  cornerStyle: CornerStyle.bothCurve,
                  gradient: getDynamicGaugeGradient(progress),
                ),
                // Background RangePointers to create depth effect
                RangePointer(
                  value: progress - 1.5,
                  width: 0.09,
                  sizeUnit: GaugeSizeUnit.factor,
                  gradient: SweepGradient(
                    colors: <Color>[
                      kTransparent,
                      kWhite.withOpacity(0.02), // Lowest opacity
                      kWhite.withOpacity(0.02),
                      kWhite.withOpacity(0.02),
                      kWhite.withOpacity(0.02),
                      kWhite.withOpacity(0.02),
                      kWhite.withOpacity(0.02),
                      kWhite.withOpacity(0.01),
                    ],
                  ),
                  cornerStyle: CornerStyle.bothCurve,
                  enableAnimation: true,
                ),

                RangePointer(
                  value: progress - 1.5,
                  width: 0.07,
                  sizeUnit: GaugeSizeUnit.factor,
                  gradient: SweepGradient(
                    colors: <Color>[
                      kTransparent,
                      kWhite.withOpacity(0.04),
                      kWhite.withOpacity(0.04),
                      kWhite.withOpacity(0.04),
                      kWhite.withOpacity(0.04),
                      kWhite.withOpacity(0.04),
                      kWhite.withOpacity(0.04),
                      kWhite.withOpacity(0.02),
                    ],
                  ),
                  cornerStyle: CornerStyle.bothCurve,
                  enableAnimation: true,
                ),

                RangePointer(
                  value: progress - 1.5,
                  width: 0.05,
                  sizeUnit: GaugeSizeUnit.factor,
                  gradient: SweepGradient(
                    colors: <Color>[
                      kTransparent,
                      kWhite.withOpacity(0.065),
                      kWhite.withOpacity(0.065),
                      kWhite.withOpacity(0.065),
                      kWhite.withOpacity(0.065),
                      kWhite.withOpacity(0.065),
                      kWhite.withOpacity(0.065),
                      kWhite.withOpacity(0.04),
                    ],
                  ),
                  cornerStyle: CornerStyle.bothCurve,
                  enableAnimation: true,
                ),

                RangePointer(
                  value: progress - 1.5,
                  width: 0.02,
                  sizeUnit: GaugeSizeUnit.factor,
                  gradient: SweepGradient(
                    colors: <Color>[
                      kTransparent,
                      kWhite.withOpacity(0.075), // Highest opacity
                      kWhite.withOpacity(0.075),
                      kWhite.withOpacity(0.075),
                      kWhite.withOpacity(0.075),
                      kWhite.withOpacity(0.075),
                      kWhite.withOpacity(0.075),
                      kWhite.withOpacity(0.06),
                    ],
                  ),
                  cornerStyle: CornerStyle.bothCurve,
                  enableAnimation: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
