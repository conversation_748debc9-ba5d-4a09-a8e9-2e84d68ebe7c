import 'dart:math' show atan, cos, exp, log, pi, pow, tan;

import 'package:animate_do/animate_do.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/constants/env_constants.dart';
import 'package:nexqloud/core/extensions/log.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/ui/widgets/blurred_background.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/features/main/models/server_info.dart';
import 'package:nexqloud/features/main/providers/server_data_provider.dart';
import 'package:nexqloud/features/main/ui/views/expanded_mobile_map_view.dart';
import 'package:nexqloud/features/main/ui/widgets/server_info_popup.dart';
import 'package:nexqloud/features/main/ui/widgets/small_server_popup_info.dart';
import 'package:nexqloud/features/main/ui/widgets/vertical_zoom_slider.dart';
import 'package:nexqloud/generated/assets.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_maps/maps.dart';

class WorldMap extends StatefulWidget {
  const WorldMap({super.key});

  @override
  State<WorldMap> createState() => _WorldMapState();
}

class _WorldMapState extends State<WorldMap>
    with SingleTickerProviderStateMixin {
  late MapShapeSource _worldMapDataSource;
  late MapZoomPanBehavior _worldMapZoomPanBehavior;
  late MapShapeLayerController _worldMapShapeController;
  late MapTileLayerController _worldMapTileLayerController;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  int _selectedContinentIndex = -1;
  int _hoveredMarkerIndex = -1;
  ServerInfo? _selectedMarker;
  double _worldMapZoomLevel = 1;
  final double _zoomLevelBound = 5;

  // Added list to manage marker indices
  List<int> _markerIndices = [];
  bool _isModalDisplayed = false;

  bool get isLiveViewOnly => EnvConstants.appEnv == Env.LIVE;

  @override
  void initState() {
    super.initState();
    // context.read<ServerDataProvider>().setData();
    _configureWorldMap();
    _worldMapShapeController = MapShapeLayerController();
    _worldMapTileLayerController = MapTileLayerController();
    _setZoomPanBehavior();

    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..repeat(reverse: true);

    // Define the animation scaling factor
    _pulseAnimation = Tween<double>(begin: 1, end: 1.3).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Initialize marker indices
    _updateMarkerIndices();
  }

  void _setZoomPanBehavior() {
    _worldMapZoomPanBehavior = MapZoomPanBehavior(
      enableDoubleTapZooming: true,
      zoomLevel: _worldMapZoomLevel,
      showToolbar: false,
      maxZoomLevel: 20,
      toolbarSettings: const MapToolbarSettings(
        itemBackgroundColor: graphlinecolor2,
        iconColor: kWhite,
        itemHoverColor: kPurpleColor,
        direction: Axis.vertical,
        position: MapToolbarPosition.bottomRight,
      ),
    );
  }

  void _configureWorldMap() {
    _worldMapDataSource = const MapShapeSource.asset(
      'assets/world_map.json',
      // shapeDataField: 'name',
      // dataCount: ,
      // primaryValueMapper: (index) => _continentsList.keys.toList()[index],
    );
  }

  // Method to update marker indices
  void _updateMarkerIndices() {
    final dataLength =
        context.read<ServerDataProvider>().serversWithLatLng.length;
    _markerIndices = List<int>.generate(dataLength, (i) => i);
    if (_selectedMarker != null) {
      final selectedIndex = context
          .read<ServerDataProvider>()
          .serversWithLatLng
          .indexOf(_selectedMarker!);
      if (selectedIndex != -1) {
        _markerIndices.remove(selectedIndex);
        _markerIndices.add(selectedIndex); // Move selected index to end
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ServerDataProvider>();

    if (provider.isRowSelected && provider.serverModel != null) {
      _onMapMarkerTapped(provider.serverModel!);
    }

    if (!provider.isRowSelected) {
      provider.serverModel = null;
    }

    if (_selectedMarker == null && provider.singleServerDataTimer != null) {
      provider.singleServerDataTimer!.cancel();
    }

    return (context.isMobile ||
                context.isMobileOrientationWide ||
                context.isTablet) &&
            provider.isMapExpanded
        ? ExpandedMobileMapView(
            worldMapZoomLevel: 5,
            initialMarkersCount: _markerIndices.length,
            isModalVisible: _isModalDisplayed,
            selectedMarker: _selectedMarker,
            onCollapsed: () {
              _setZoomLevel(1);
            },
            toggleModal: (value) {
              _isModalDisplayed = value;
              if (_isModalDisplayed) {
                _isModalDisplayed = false;
                _selectedMarker = null;
              }
            },
          )
        : Container(
            height: context.isMobileOrientationWide
                ? context.height * 0.88
                : context.isMobile
                    ? context.height * 0.45
                    : context.isTablet
                        ? context.height * 0.5
                        : context.isDesktopExtraSmall
                            ? context.height * 0.62
                            : context.height * 0.58,
            width: isLiveViewOnly ? context.width : context.horizontalPadding,
            margin: const EdgeInsets.only(
              bottom: 16,
              left: 24,
              right: 24,
            ),
            decoration: BoxDecoration(
              border: GradientBoxBorder(
                gradient: LinearGradient(
                  colors: [
                    kWhite.withOpacity(0.2),
                    kWhite.withOpacity(0.1),
                    const Color(0xFF4e65b3).withOpacity(0.5),
                    kWhite.withOpacity(0.1),
                    kWhite.withOpacity(0.02),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              borderRadius: BorderRadius.circular(30),
            ),
            child: DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                gradient: LinearGradient(
                  colors: [
                    kWhite.withOpacity(0.1),
                    kTransparent,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(
                  vertical: (context.isTablet || context.isMobile)
                      ? _worldMapZoomLevel >= 5
                          ? 0
                          : 20
                      : 24,
                  horizontal: (context.isTablet || context.isMobile)
                      ? _worldMapZoomLevel >= 5
                          ? 0
                          : 10
                      : 24,
                ),
                child: Column(
                  children: [
                    if (_selectedContinentIndex != -1)
                      Align(
                        alignment: Alignment.centerLeft,
                        child: CupertinoButton(
                          padding: EdgeInsets.zero,
                          minSize: 0,
                          onPressed: () {
                            setState(() {
                              _selectedContinentIndex = -1;
                            });
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.arrow_back_ios,
                                size: 18,
                                color: Colors.white,
                              ),
                              Text(
                                'Back to Globe',
                                style: context.medium?.copyWith(
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    if (context.isDesktop && _selectedContinentIndex != -1)
                      const Space.vertical(20),
                    if (_selectedContinentIndex == -1) ...[
                      Expanded(
                        child: SizedBox(
                          height: context.isDesktop
                              ? context.height * 0.55
                              : context.height * 0.35,
                          child: Listener(
                            onPointerDown: (event) {
                              // Check if the tap is outside the bounds of the widget (if needed)
                              if (_selectedMarker != null) {
                                setState(() {
                                  _hoveredMarkerIndex = -1;
                                  _selectedMarker = null;
                                  provider.isRowSelected = false;
                                  // Perform the action immediately

                                  _updateWhenMarkerUnselected();
                                });
                              }
                            },
                            behavior: HitTestBehavior.translucent,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(20),
                                  child: SfMapsTheme(
                                    data: SfMapsThemeData(
                                      shapeHoverColor: kTransparent,
                                      shapeHoverStrokeColor:
                                          kWhite.withOpacity(0.22),
                                      tooltipColor: kTransparent,
                                      tooltipStrokeColor: kTransparent,
                                      tooltipBorderRadius:
                                          BorderRadius.circular(8),
                                    ),
                                    child: ColoredBox(
                                      color: _worldMapZoomLevel >= 5
                                          ? const Color(0xFFD4DADC)
                                          : kTransparent,
                                      child: SfMaps(
                                        layers: <MapLayer>[
                                          MapShapeLayer(
                                            strokeWidth: 0,
                                            source: _worldMapDataSource,
                                            initialMarkersCount:
                                                _markerIndices.length,
                                            color: kWhite.withOpacity(0.2),
                                            strokeColor:
                                                kWhite.withOpacity(0.22),
                                            controller:
                                                _worldMapShapeController,
                                            zoomPanBehavior:
                                                _worldMapZoomPanBehavior,
                                            selectedIndex:
                                                _selectedContinentIndex,
                                            markerBuilder: _buildMapMarker,
                                            markerTooltipBuilder:
                                                (context, index) {
                                              // if (!context.isDesktopDevices) {
                                              //   return const SizedBox();
                                              // }

                                              if (_hoveredMarkerIndex == -1) {
                                                return const SizedBox();
                                              }

                                              if (index == -1) {
                                                return const SizedBox();
                                              }

                                              final dataIndex =
                                                  _markerIndices[index];
                                              final marker = context
                                                  .read<ServerDataProvider>()
                                                  .serversWithLatLng[dataIndex];
                                              return SizedBox(
                                                height: 40,
                                                width: 120,
                                                child: TooltipBubble(
                                                  text: marker.licenseKey,
                                                ),
                                              );
                                            },
                                          ),
                                          if (_worldMapZoomLevel >=
                                              _zoomLevelBound)
                                            MapTileLayer(
                                              urlTemplate:
                                                  'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png',
                                              initialMarkersCount:
                                                  _markerIndices.length,
                                              initialFocalLatLng:
                                                  _selectedMarker != null
                                                      ? MapLatLng(
                                                          _selectedMarker!
                                                              .latitude,
                                                          _selectedMarker!
                                                              .longitude,
                                                        )
                                                      : _worldMapZoomPanBehavior
                                                              .focalLatLng ??
                                                          const MapLatLng(0, 0),
                                              controller:
                                                  _worldMapTileLayerController,
                                              initialZoomLevel:
                                                  _worldMapZoomLevel.toInt(),
                                              zoomPanBehavior:
                                                  _worldMapZoomPanBehavior,
                                              markerBuilder: _buildMapMarker,
                                              markerTooltipBuilder:
                                                  (context, index) {
                                                // if (!context.isDesktopDevices) {x
                                                //   return const SizedBox();
                                                // }

                                                if (_hoveredMarkerIndex == -1) {
                                                  return const SizedBox();
                                                }

                                                if (index == -1) {
                                                  return const SizedBox();
                                                }

                                                final dataIndex =
                                                    _markerIndices[index];
                                                final marker = context
                                                    .read<ServerDataProvider>()
                                                    .serversWithLatLng[dataIndex];

                                                if (marker == _selectedMarker) {
                                                  return const SizedBox();
                                                }

                                                return SizedBox(
                                                  height: 40,
                                                  width: 120,
                                                  child: TooltipBubble(
                                                    text: marker.licenseKey,
                                                  ),
                                                );
                                              },
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                if (context.isDesktop || context.isDesktopLarge)
                                  if (_selectedMarker != null)
                                    Align(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          if (context.isDesktopExtraSmall ||
                                              context.isDesktopSmall) ...[
                                            SmallServerPopupInfo(
                                              server: _selectedMarker!,
                                            ),
                                          ] else ...[
                                            const Space.horizontal(12),
                                            ServerInfoPopup(
                                              server: _selectedMarker!,
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),
                                Positioned(
                                  bottom: 15,
                                  right: 15,
                                  child: VerticalZoomSlider(
                                    zoomValue: _worldMapZoomLevel,
                                    maxZoom: 15,
                                    onChanged: _setZoomLevel,
                                  ),
                                ),
                                if (!context.isDesktop)
                                  Positioned(
                                    top: 15,
                                    right: 15,
                                    child: GestureDetector(
                                      onTap: () {
                                        context
                                            .read<ServerDataProvider>()
                                            .toggleMapExpand(true);
                                      },
                                      child: BlurredBackground(
                                        borderRadius: 9,
                                        child: Container(
                                          height: context.isTablet ? 42 : 32,
                                          width: context.isTablet ? 42 : 32,
                                          padding: const EdgeInsets.all(8),
                                          decoration: _worldMapZoomLevel >= 5
                                              ? ShapeDecoration(
                                                  color: Colors.black
                                                      .withOpacity(0.0001),
                                                  shape: RoundedRectangleBorder(
                                                    side: BorderSide(
                                                      width: 0.50,
                                                      color: Colors.black
                                                          .withOpacity(
                                                        0.10000000149011612,
                                                      ),
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                      8,
                                                    ),
                                                  ),
                                                )
                                              : BoxDecoration(
                                                  color: _worldMapZoomLevel < 5
                                                      ? kBorderColor
                                                          .withOpacity(0.12)
                                                      : graphlinecolor2,
                                                  borderRadius:
                                                      BorderRadius.circular(9),
                                                  gradient:
                                                      _worldMapZoomLevel >= 5
                                                          ? const LinearGradient(
                                                              colors: [
                                                                gradientColorOne,
                                                                gradientColorTwo,
                                                              ],
                                                            )
                                                          : null,
                                                ),
                                          child: SvgPicture.asset(
                                            Assets.svgExpandMap,
                                            height: 40,
                                            width: 40,
                                            color: _worldMapZoomLevel >= 5
                                                ? kBlack
                                                : null,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ] else ...[
                      // Expanded(
                      //   child: SizedBox(
                      //     height: context.isDesktop
                      //         ? context.height * 0.55
                      //         : context.height * 0.35,
                      //     child: Column(
                      //       children: [
                      //         Expanded(
                      //           child: FadeIn(
                      //             child: ContinentMap(
                      //               selectedMarkerIndex:
                      //                   _selectedContinentIndex,
                      //               serverModel: _selectedMarker,
                      //               isSelectedAMarker: _isSelectedAMarker,
                      //               continent: _continentsList.keys
                      //                   .toList()[_selectedContinentIndex],
                      //               filePath: _continentsList.values
                      //                   .toList()[_selectedContinentIndex],
                      //             ),
                      //           ),
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      // ),
                    ],
                  ],
                ),
              ),
            ),
          );
  }

  // update when marker unselected
  void _updateWhenMarkerUnselected() {
    _worldMapTileLayerController.updateMarkers(
      List<int>.generate(_markerIndices.length, (i) => i),
    );
    _worldMapShapeController.updateMarkers(
      List<int>.generate(_markerIndices.length, (i) => i),
    );
  }

  MapMarker _buildMapMarker(BuildContext context, int index) {
    final dataIndex = _markerIndices[index];
    double markerSize;

    markerSize = 10.0;

    final marker =
        context.read<ServerDataProvider>().serversWithLatLng[dataIndex];

    return MapMarker(
      latitude: marker.latitude,
      longitude: marker.longitude,
      child: MouseRegion(
        onHover: (_) => _onHoveredMarker(index),
        onExit: (_) => _onExitedMarker(index),
        child: ZoomIn(
          child: GestureDetector(
            onTap: () => _onMapMarkerTapped(marker),
            child: Builder(
              builder: (context) {
                final isSelected = _selectedMarker == marker;
                final isHovered = _hoveredMarkerIndex == index;

                return AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: isSelected
                          ? context.isDesktop || context.isDesktopLarge
                              ? _pulseAnimation.value
                              : 1.0
                          : 1.0,
                      child: Container(
                        height: markerSize,
                        width: markerSize,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: isSelected || isHovered
                              ? [
                                  const BoxShadow(
                                    blurRadius: 15,
                                    spreadRadius: 2,
                                    color: Color(0xFF935BDA),
                                  ),
                                ]
                              : null,
                          color: isSelected || isHovered
                              ? const Color(0xFF935BDA)
                              : graphlinecolor2.withOpacity(0.8),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _setZoomLevel(double value) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final newZoom = value.clamp(1.0, 15.0);
      _worldMapZoomPanBehavior.zoomLevel = newZoom;
      _worldMapZoomLevel = newZoom;

      if (value == 1) {
        _setZoomPanBehavior();
      }
      if (value < 5) _selectedMarker = null;

      setState(() {});
    });
  }

  void _onHoveredMarker(int index) {
    if (context.isMobile || context.isTablet) {
      return;
    }

    if (_hoveredMarkerIndex != index) {
      _hoveredMarkerIndex = index;

      _worldMapShapeController.updateMarkers([index]);
      _worldMapTileLayerController.updateMarkers([index]);
    } else {
      _hoveredMarkerIndex = index;
    }
  }

  void _onExitedMarker(int index) {
    if (context.isMobile || context.isTablet) {
      return;
    }
    if (_hoveredMarkerIndex != -1) {
      _hoveredMarkerIndex = -1;
      _worldMapShapeController.updateMarkers([index]);
      _worldMapTileLayerController.updateMarkers([index]);
    } else {
      _hoveredMarkerIndex = -1;
    }
  }

  void _onMapMarkerTapped(ServerInfo marker) {
    if (marker.dontHaveLatLng) {
      if (_selectedMarker != null) {
        // setState(() {
        context.read<ServerDataProvider>().isRowSelected = false;

        _hoveredMarkerIndex = -1;
        _selectedMarker = null;
        // Perform the action immediately
        _worldMapZoomPanBehavior.zoomLevel = 14;
        _worldMapZoomLevel = 14;

        _updateWhenMarkerUnselected();
        // });
      }

      // final random = Random();
      // final randomLat = random.nextDouble() * 180 - 90;
      // final randomLng = random.nextDouble() * 360 - 180;
      // marker = marker.copyWith(
      //   latitude: randomLat,
      //   longitude: randomLng,
      // );
      // return;
    } else {
      if (_selectedMarker != null) {
        // setState(() {
        _hoveredMarkerIndex = -1;
        // _selectedMarker = null;
        // Perform the action immediately
        // _worldMapZoomPanBehavior.zoomLevel = 14;
        // _worldMapZoomLevel = 14;

        //   _updateWhenMarkerUnselected();
        // });
      }
    }

    if (context.isMobile || context.isTablet) {
      _selectedMarker = marker;
      context.read<ServerDataProvider>().toggleMapExpand(true);
      return;
    }

    // Update previous selected marker
    if (_selectedMarker != null) {
      final previousDataIndex = context
          .read<ServerDataProvider>()
          .serversWithLatLng
          .indexOf(_selectedMarker!);
      if (previousDataIndex == -1) {
        'Previous Data Index is -1'.printError();
        return;
      }

      final previousMarkerIndex = _markerIndices.indexOf(previousDataIndex);
      if (_worldMapZoomLevel < _zoomLevelBound) {
        _worldMapShapeController.updateMarkers([previousMarkerIndex]);
      } else {
        _worldMapTileLayerController.updateMarkers([previousMarkerIndex]);
      }
    }

    _selectedMarker = marker;
    _updateMarkerIndices(); // Update marker indices since selected marker changed
    _hoveredMarkerIndex = -1;

    // Rebuild markers to reflect the new order
    if (_worldMapZoomLevel < _zoomLevelBound) {
      _worldMapShapeController
          .updateMarkers(List<int>.generate(_markerIndices.length, (i) => i));
    } else {
      _worldMapTileLayerController
          .updateMarkers(List<int>.generate(_markerIndices.length, (i) => i));
    }

    // Set the zoom level directly to 15 on marker tap
    _worldMapZoomPanBehavior.zoomLevel = 15;
    _worldMapZoomLevel = 15;

    // Update focalLatLng to center on the tapped marker
    if (_selectedMarker!.latitude == 0 && _selectedMarker!.longitude == 0) {
      // Invalid coordinates, skipping map update
      return;
    }

    final lat = _selectedMarker!.latitude;
    final lon = _selectedMarker!.longitude;

    // With a zoom level of 15, calculate the new focal point
    final pixelOffsetY = context.isDesktopExtraSmall
        ? 170
        : context.isDesktopSmall
            ? 183
            : 200;
    double sinhCustom(double x) => (exp(x) - exp(-x)) / 2;

    final adjustedZoom = _worldMapZoomPanBehavior.zoomLevel;
    final adjustedN = pow(2.0, adjustedZoom);
    final latRad = lat * pi / 180.0;
    final adjustedXTile = (lon + 180.0) / 360.0 * adjustedN;
    final adjustedYTile =
        (1.0 - log(tan(latRad) + 1 / cos(latRad)) / pi) / 2.0 * adjustedN;

    final adjPixelX = adjustedXTile * 256.0;
    var adjPixelY = adjustedYTile * 256.0;

    adjPixelY -= pixelOffsetY; // Apply the offset

    final adjNewXTile = adjPixelX / 256.0;
    final adjNewYTile = adjPixelY / 256.0;

    final adjNewLon = adjNewXTile / adjustedN * 360.0 - 180.0;
    final adjNewLatRad =
        atan(sinhCustom(pi * (1 - 2 * adjNewYTile / adjustedN)));
    final adjNewLat = adjNewLatRad * 180.0 / pi;

    _worldMapZoomPanBehavior.focalLatLng = MapLatLng(adjNewLat, adjNewLon);

    setState(() {});
  }
}
