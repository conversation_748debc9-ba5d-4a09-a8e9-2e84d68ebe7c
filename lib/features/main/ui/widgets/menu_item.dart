import 'package:flutter/material.dart';

import 'package:nexqloud/core/constants/colors.dart';

class MenuItem extends StatefulWidget {
  const MenuItem({
    super.key,
    required this.text,
    required this.onTap,
  });
  final String text;
  final VoidCallback onTap;

  @override
  State<MenuItem> createState() => _MenuItemState();
}

class _MenuItemState extends State<MenuItem> {
  bool isHovering = false;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      onHover: (value) {
        setState(() {
          isHovering = value;
        });
      },
      child: Row(
        children: [
          Expanded(
            child: Container(
              color: kTransparent,
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Text(
                widget.text,
                style: TextStyle(
                  color: isHovering ? kPurpleColor : Colors.white,
                  fontSize: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
