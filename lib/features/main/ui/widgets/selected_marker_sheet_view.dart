import 'package:flutter/material.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/double.dart';
import 'package:nexqloud/core/extensions/responsive_font.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/ui/widgets/info_chip.dart';
import 'package:nexqloud/core/ui/widgets/invite_status_tile.dart';
import 'package:nexqloud/core/ui/widgets/shimmer_loading.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/core/utils/data_size.dart';
import 'package:nexqloud/features/main/models/server_info.dart';
import 'package:nexqloud/features/main/providers/server_data_provider.dart';
import 'package:nexqloud/features/main/ui/widgets/online_server_indicator.dart';
import 'package:nexqloud/generated/assets.dart';
import 'package:provider/provider.dart';

class SelectedMarkerSheetView extends StatefulWidget {
  const SelectedMarkerSheetView({super.key, this.selectedMarker});
  final ServerInfo? selectedMarker;

  @override
  State<SelectedMarkerSheetView> createState() =>
      _SelectedMarkerSheetViewState();
}

class _SelectedMarkerSheetViewState extends State<SelectedMarkerSheetView> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        _isLoading = true;
      });
      if (widget.selectedMarker != null) {
        final provider = context.read<ServerDataProvider>();
        await provider
            .fetchSingleServerDataViaServerId(widget.selectedMarker!.serverId);
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ServerDataProvider>();

    return Consumer<ServerDataProvider>(
      builder: (context, _, __) {
        return Shimmer(
          linearGradient: shimmerGradient,
          child: ShimmerLoading(
            isLoading: _isLoading,
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                gradient: LinearGradient(
                  colors: [
                    Color(0xFF382565),
                    Color(0xFF28286F),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 16,
              ),
              height: context.height < 600 ? context.height * 0.5 : null,
              child: SingleChildScrollView(
                physics: context.height < 600
                    ? null
                    : const NeverScrollableScrollPhysics(),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    provider.fetchedServerData.model ?? '',
                                    style: context.medium?.copyWith(
                                      fontSize: context.responsiveFontSize(24),
                                    ),
                                  ),
                                  const Space.horizontal(8),
                                  OnlineServerIndicator(
                                    isOnline:
                                        provider.fetchedServerData.isOnline,
                                  ),
                                ],
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                margin: const EdgeInsets.only(right: 8),
                                decoration: BoxDecoration(
                                  color: kWhite,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Colors.black12,
                                      blurRadius: 0.04,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  children: [
                                    const Icon(
                                      Icons.location_on_outlined,
                                      color: kBlack,
                                      size: 14,
                                    ),
                                    const Space.horizontal(3),
                                    Text(
                                      provider.fetchedServerData.location.city
                                                  .isEmpty &&
                                              provider.fetchedServerData
                                                  .location.country.isEmpty
                                          ? 'Unknown'
                                          : '${provider.fetchedServerData.location.city}${provider.fetchedServerData.location.city.isNotEmpty && provider.fetchedServerData.location.country.isNotEmpty ? ', ' : ''}${provider.fetchedServerData.location.country}',
                                      style: context.normal!.copyWith(
                                        color: kBlack,
                                        fontSize:
                                            context.responsiveFontSize(14),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const Space.vertical(4),
                          Text(
                            provider.fetchedServerData.gpu.isNotEmpty
                                ? '${provider.fetchedServerData.gpu.first.vendor} | ${provider.fetchedServerData.gpu.first.model}'
                                : 'Integrated Graphics',
                            style: context.normal!.copyWith(
                              fontSize: context.responsiveFontSize(14),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Space.vertical(16),
                    Row(
                      children: [
                        Expanded(
                          child: InfoChip(
                            icon: Assets.svgUptimeClockIcon,
                            value:
                                '${provider.fetchedServerData.uptimePercentage.toStringAsFixed(2)}%',
                          ),
                        ),
                        const Space.horizontal(10),
                        Expanded(
                          child: InfoChip(
                            icon: Assets.svgQloudScoreCloudIcon,
                            value: provider
                                .fetchedServerData.cloudScore.toPercentage,
                          ),
                        ),
                        const Space.horizontal(10),
                        Expanded(
                          child: InfoChip(
                            icon: Assets.svgLicenseKeyIcon,
                            value: provider.fetchedServerData.nftTokenId ?? '',
                          ),
                        ),
                      ],
                    ),
                    const Space.vertical(10),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: kWhite.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: kWhite.withOpacity(0.2),
                          width: 0.5,
                        ),
                      ),
                      child: Column(
                        children: [
                          InviteStatusTile(
                            title: 'Network',
                            value: provider.fetchedServerData.networkInbound
                                .toStringAsFixed(2),
                            unit: 'GBit/s',
                            progress: provider.fetchedServerData.percentages
                                .networkInboundPercentile,
                          ),
                          InviteStatusTile(
                            title: 'CPU',
                            value: provider.fetchedServerData.cpu
                                .toStringAsFixed(2),
                            unit: '%',
                            progress: provider.fetchedServerData.percentages
                                .cpuUtilizationPercentile,
                          ),
                          InviteStatusTile(
                            title: 'RAM',
                            value: provider
                                .fetchedServerData.memoryUsagePercentage
                                .toStringAsFixed(2),
                            unit: '%',
                            progress: provider.fetchedServerData.percentages
                                .ramUtilizationPercentile,
                          ),
                          InviteStatusTile(
                            title: 'Disk',
                            value: formatBytes(
                              provider.fetchedServerData.diskRead,
                              showUnits: false,
                            ),
                            progress: provider.fetchedServerData.percentages
                                .diskReadPercentile,
                            unit: dataSizeUnit(
                              provider.fetchedServerData.diskRead,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Space.vertical(
                      MediaQuery.viewInsetsOf(context).bottom + 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
