import 'package:flutter/material.dart';

import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/extensions/theme_ext.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';

class ServerWidget extends StatelessWidget {
  const ServerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 400,
      padding: const EdgeInsets.all(12),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/png/server_modal_bg.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                width: 278,
                height: 150,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: const LinearGradient(
                    colors: [Color(0xFF1A73E8), Color(0xFF1A1A2E)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  border: Border.all(
                    width: 4,
                    color: const Color(0xFF33B1FF),
                  ),
                  image: const DecorationImage(
                    image: AssetImage('assets/images/png/cpu.png'),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Container(
                width: 278,
                height: 150,
                padding: const EdgeInsets.all(10),
                alignment: Alignment.bottomLeft,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF33B1FF),
                      kTransparent,
                    ],
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      'Zeus Pro',
                      style: context.semiBold!
                          .copyWith(fontSize: 18, color: kWhite),
                    ),
                    const Space.vertical(6),
                  ],
                ),
              ),
            ],
          ),
          const Space.vertical(15),
          Image.asset(
            'assets/images/png/server_info_one.png',
          ),
        ],
      ),
    );
    return Container(
      width: 300,
      height: 600,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: const LinearGradient(
          colors: [Color(0xFF170852), Color(0xFF170852)],
        ),
      ),
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                width: 278,
                height: 150,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: const LinearGradient(
                    colors: [Color(0xFF1A73E8), Color(0xFF1A1A2E)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  image: const DecorationImage(
                    image: AssetImage('assets/images/png/cpu.png'),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Container(
                width: 278,
                height: 150,
                padding: const EdgeInsets.all(10),
                alignment: Alignment.bottomLeft,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF33B1FF),
                      Color(0xFF33B1FF),
                      kTransparent,
                      kTransparent,
                    ],
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      'Zeus Pro',
                      style: context.semiBold!
                          .copyWith(fontSize: 18, color: kWhite),
                    ),
                    const Space.vertical(6),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class ServerDetail extends StatelessWidget {
  const ServerDetail({required this.label, required this.value, super.key});
  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
