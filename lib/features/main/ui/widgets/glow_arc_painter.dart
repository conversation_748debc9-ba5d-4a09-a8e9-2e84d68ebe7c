import 'dart:math' as math;

import 'package:flutter/material.dart';

class GlowArcPainterHalfGauge extends CustomPainter {
  GlowArcPainterHalfGauge({required this.progress, required this.glowColor});
  final double progress; // Progress value (0 to 100)
  final Color glowColor;

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate the start and sweep angles in radians
    const startAngle = 1.35 * math.pi / 2; // Starting at -90 degrees
    final sweepAngle = (progress / 100) * 1.7 * math.pi;

    // Define the center and radius
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.8;

    // Create a rect for the arc
    final rect = Rect.fromCircle(center: center, radius: radius);

    // Create the glow paint
    final glowPaint = Paint()
      ..shader = SweepGradient(
        colors: [
          glowColor.withOpacity(0),
          glowColor.withOpacity(0),
          glowColor.withOpacity(0.5),
          glowColor.withOpacity(0.2),
        ],
        stops: const [0.0, 0.2, 0.5, 1.0],
      ).createShader(rect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 20.0 // Adjust the stroke width for the glow size
      ..maskFilter = const MaskFilter.blur(
        BlurStyle.normal,
        15,
      ); // Adjust blur for glow intensity

    // Draw the glowing arc
    canvas.drawArc(rect, startAngle, sweepAngle, false, glowPaint);
  }

  @override
  bool shouldRepaint(covariant GlowArcPainterHalfGauge oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.glowColor != glowColor;
  }
}

class GlowArcPainterCircular extends CustomPainter {
  GlowArcPainterCircular({required this.progress, required this.glowColor});
  final double progress; // Progress value (0 to 100)
  final Color glowColor;

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate the start and sweep angles in radians
    const startAngle = -math.pi / 2; // Starting at -90 degrees (top)
    final sweepAngle = (progress / 100) * 2 * math.pi;

    // Define the center and radius
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    // Create a rect for the arc
    final rect = Rect.fromCircle(center: center, radius: radius);

    // Create the glow paint
    final glowPaint = Paint()
      ..shader = SweepGradient(
        colors: [
          // Colors.transparent,
          glowColor.withOpacity(0.5),
          // Colors.transparent,
        ],
        stops: const [
          0.0,
        ],
      ).createShader(rect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 15.0 // Adjust the stroke width for the glow size
      ..maskFilter = const MaskFilter.blur(
        BlurStyle.normal,
        10,
      ); // Adjust blur for glow intensity

    // Save the canvas state
    canvas.save();

    // Rotate the canvas to align with the startAngle
    canvas.translate(center.dx, center.dy);
    canvas.rotate(startAngle);
    canvas.translate(-center.dx, -center.dy);

    // Draw the glowing arc along the progress
    canvas.drawArc(rect, 0, sweepAngle, false, glowPaint);

    // Restore the canvas state
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant GlowArcPainterCircular oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.glowColor != glowColor;
  }
}

class InnerDepthPainter extends CustomPainter {
  InnerDepthPainter({required this.progress, required this.depthColor});
  final double progress; // Progress value (0 to 100)
  final Color depthColor;

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate the start and sweep angles in radians
    const startAngle = -math.pi / 2; // Starting at -90 degrees (top)
    final sweepAngle = (progress / 100) * 2 * math.pi;

    // Define the center and radii
    final center = Offset(size.width / 2, size.height / 2);
    final outerRadius = size.width / 2.28; // Adjust as needed
    const thickness = 1.0; // Adjust thickness
    final innerRadius = outerRadius - thickness;

    // Create paths for the outer and inner arcs
    final outerRect = Rect.fromCircle(center: center, radius: outerRadius);
    final innerRect = Rect.fromCircle(center: center, radius: innerRadius);

    final path = Path()
      ..addArc(outerRect, startAngle, sweepAngle)
      ..arcTo(innerRect, startAngle + sweepAngle, -sweepAngle, false)
      ..close();

    // Save the current canvas state
    canvas.save();

    // Clip the canvas to the path to confine the inner shadow
    canvas.clipPath(path);

    // Create a paint for the inner shadow
    final shadowPaint = Paint()
      ..color = depthColor.withOpacity(0.1) // Adjust opacity as needed
      ..maskFilter =
          const MaskFilter.blur(BlurStyle.normal, 4); // Adjust blur radius

    // Draw a larger circle to simulate the inner shadow effect
    // The circle should be larger than the outer radius to ensure the blur spreads inward
    canvas.drawCircle(center, outerRadius + 10, shadowPaint);

    // Restore the canvas state
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant InnerDepthPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.depthColor != depthColor;
  }
}
