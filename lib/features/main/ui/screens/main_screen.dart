import 'package:animate_do/animate_do.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nexqloud/core/constants/colors.dart';
import 'package:nexqloud/core/constants/env_constants.dart';
import 'package:nexqloud/core/extensions/log.dart';
import 'package:nexqloud/core/extensions/size_ext.dart';
import 'package:nexqloud/core/ui/widgets/space.dart';
import 'package:nexqloud/features/main/providers/server_data_provider.dart';
import 'package:nexqloud/features/main/ui/views/footer_area.dart';
import 'package:nexqloud/features/main/ui/widgets/data_analysis_gauges.dart';
import 'package:nexqloud/features/main/ui/widgets/data_grid.dart';
import 'package:nexqloud/features/main/ui/widgets/landing_page_description.dart';
import 'package:nexqloud/features/main/ui/widgets/main_app_bar.dart';
import 'package:nexqloud/features/main/ui/widgets/mobile_menu_container.dart';
import 'package:nexqloud/features/main/ui/widgets/world_map.dart';
import 'package:provider/provider.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  late AnimationController _controller;
  late ScrollController _scrollController;
  late ScrollController _sliverScrollController;
  late AnimationController _opacityController;
  late Animation<double> _opacityAnimation;

  bool _isMenuVisible = false; // Controls the visibility of the menu
  final double _menuMaxHeight = 300; // Max height for the menu
  bool _isHeaderVisible = false;
  final DraggableScrollableController _dragScrollController =
      DraggableScrollableController();
  final double _maxExtent = 0.9;
  final double _minExtent = 0.46;
  final double _initialExtent = 0.46;
  bool isMobileDevice = false;
  WebBrowserInfo? _webBrowserInfo;
  final _leaderBoardGlobalOBjectKey = GlobalKey();

  bool get isLiveViewOnly => EnvConstants.appEnv == Env.LIVE;

  @override
  void initState() {
    super.initState();

    _opacityController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _opacityAnimation = Tween<double>(begin: 1, end: 0).animate(
      CurvedAnimation(parent: _opacityController, curve: Curves.easeInOut),
    );

    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scrollController = ScrollController()..addListener(_scrollListener);
    _sliverScrollController = ScrollController();

    _dragScrollController.addListener(_onDragScroll);

    Future.delayed(Duration.zero, () async {
      final deviceInfo = DeviceInfoPlugin();
      final webBrowserInfo = await deviceInfo.webBrowserInfo;
      setState(() {
        _webBrowserInfo = webBrowserInfo;
      });
      // final deviceInfos = await deviceInfo.deviceInfo;
      /// Do not remove these logs
      // print('Running on ${deviceInfos.data}');
      // print('Running on ${webBrowserInfo.userAgent}');
      // print('Running on ${webBrowserInfo.product}');
      // print('Running on ${webBrowserInfo.platform}');
      isMobileDevice = webBrowserInfo.userAgent?.contains('Mobile') ??
          false || context.isTablet;
    });
  }

  void _onDragScroll() {
    if (!mounted) return;
    if (_dragScrollController.pixels <= kToolbarHeight) {
      // Check if at initial position
      _opacityController.reverse(); // Reset opacity animation
    } else {
      final extent = _dragScrollController.size;
      final normalizedValue = (extent - _minExtent) / (_maxExtent - _minExtent);

      _opacityController.value = normalizedValue.clamp(0.0, 1.0);
    }
  }

  void _scrollListener() {
    // Toggle the header's visibility based on scroll offset
    if (_scrollController.offset > kToolbarHeight && !_isHeaderVisible) {
      setState(() => _isHeaderVisible = true);
    } else if (_scrollController.offset <= kToolbarHeight && _isHeaderVisible) {
      setState(() => _isHeaderVisible = false);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    _dragScrollController.dispose();
    _sliverScrollController.dispose();
    _opacityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.isDesktop.printInfo(tag: 'DESKTOP');
    context.isMobile.printInfo(tag: 'MOBILE');
    context.isTablet.printInfo(tag: 'TABLET');

    final provider = context.watch<ServerDataProvider>();

    return provider.isLoading
        ? Scaffold(
            backgroundColor: kBackgroundColor,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    'assets/icons/svg/nex_logo.svg',
                    height: 50,
                  ),
                  const Space.vertical(20),
                  const CircularProgressIndicator(
                    strokeWidth: 1.5,
                    valueColor: AlwaysStoppedAnimation(kWhite),
                  ),
                ],
              ),
            ),
          )
        : Scaffold(
            body: context.isMobile || context.isTablet
                ? Consumer<ServerDataProvider>(
                    builder: (context, _, child) {
                      return Center(
                        child: Stack(
                          clipBehavior: Clip.none,
                          alignment: Alignment.center,
                          fit: StackFit.expand,
                          children: [
                            Container(
                              decoration: const BoxDecoration(
                                color: kBackgroundColor,
                              ),
                            ),
                            Positioned(
                              left: 0,
                              top: -500,
                              child: Image.asset(
                                'assets/images/png/right.png',
                              ),
                            ),
                            Positioned(
                              right: 0,
                              top: 100,
                              child: Image.asset(
                                'assets/images/png/top_right.png',
                              ),
                            ),
                            Positioned(
                              left: 200,
                              bottom: -500,
                              child: Image.asset(
                                'assets/images/png/bottom_right.png',
                              ),
                            ),
                            if (isMobileDevice) ...[
                              Column(
                                children: [
                                  AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    color: _isMenuVisible
                                        ? kBackgroundColor
                                        : kTransparent,
                                    child: MainAppBar(
                                      controller: _controller,
                                      toggleMenu: _toggleMenu,
                                    ),
                                  ),
                                  const Space.vertical(10),
                                  Expanded(
                                    child: CustomScrollView(
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      slivers: [
                                        // Collapsible WorldMap
                                        SliverAppBar(
                                          pinned: true,
                                          expandedHeight: !provider
                                                  .isMapExpanded
                                              ? context.isMobile
                                                  ? context.height * 0.46
                                                  : context.isTablet
                                                      ? context.height * 0.7
                                                      : context.height * 0.58
                                              : context.height *
                                                  0.945, // Adjust height as needed
                                          automaticallyImplyLeading: false,
                                          backgroundColor: kTransparent,
                                          elevation: 0,
                                          flexibleSpace: FlexibleSpaceBar(
                                            background: AnimatedBuilder(
                                              animation: _opacityAnimation,
                                              builder: (context, child) {
                                                return Opacity(
                                                  opacity:
                                                      _opacityAnimation.value,
                                                  child: const WorldMap(),
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              if (!provider.isMapExpanded)
                                DraggableScrollableSheet(
                                  controller: _dragScrollController,
                                  maxChildSize: _maxExtent,
                                  initialChildSize: _initialExtent,
                                  minChildSize: _minExtent,
                                  builder: (context, scrollController) {
                                    return SingleChildScrollView(
                                      controller: scrollController,
                                      physics:
                                          const AlwaysScrollableScrollPhysics(),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 24,
                                        ),
                                        child: const Column(
                                          children: [
                                            DataAnalysisGauges(),
                                            // if (context.isDesktop)
                                            //   const TransparentDataGrid(),
                                            LandingPageDescription(),
                                            FooterArea(),
                                            Space.vertical(34),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                            ] else ...[
                              Column(
                                children: [
                                  AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    color: _isMenuVisible
                                        ? kBackgroundColor
                                        : kTransparent,
                                    child: MainAppBar(
                                      controller: _controller,
                                      toggleMenu: _toggleMenu,
                                    ),
                                  ),
                                  const Space.vertical(10),
                                  Expanded(
                                    child: CustomScrollView(
                                      controller: _sliverScrollController,
                                      physics: provider.isMapExpanded
                                          ? const NeverScrollableScrollPhysics()
                                          : const BouncingScrollPhysics(),
                                      slivers: [
                                        // Collapsible WorldMap
                                        SliverAppBar(
                                          pinned: true,
                                          expandedHeight: !provider
                                                  .isMapExpanded
                                              ? context.isMobileOrientationWide
                                                  ? context.height * 0.7
                                                  : context.isMobile
                                                      ? context.height * 0.46
                                                      : context.isTablet
                                                          ? context.height * 0.7
                                                          : context.height *
                                                              0.58
                                              : context.height *
                                                  0.945, // Adjust height as needed
                                          automaticallyImplyLeading: false,
                                          backgroundColor: kTransparent,
                                          elevation: 0,
                                          flexibleSpace: const FlexibleSpaceBar(
                                            background: WorldMap(),
                                          ),
                                        ),

                                        // The rest of the content
                                        if (!provider.isMapExpanded)
                                          const SliverToBoxAdapter(
                                            child: Padding(
                                              padding: EdgeInsets.symmetric(
                                                horizontal: 24,
                                              ),
                                              child: Column(
                                                children: [
                                                  AbsorbPointer(
                                                    child: DataAnalysisGauges(),
                                                  ),
                                                  // if (context.isDesktop)
                                                  //   const TransparentDataGrid(),
                                                  LandingPageDescription(),
                                                  FooterArea(),
                                                  Space.vertical(34),
                                                ],
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                            // Mobile Menu Container
                            AnimatedPositioned(
                              duration: const Duration(milliseconds: 300),
                              top: _isMenuVisible
                                  ? kToolbarHeight
                                  : -_menuMaxHeight,
                              left: 0,
                              right: 0,
                              child: MobileMenuContainer(
                                menuMaxHeight: _menuMaxHeight,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  )
                : Consumer<ServerDataProvider>(
                    builder: (context, _, child) => Stack(
                      children: [
                        Container(
                          decoration: const BoxDecoration(
                            color: kBackgroundColor,
                          ),
                        ),
                        Center(
                          child: Stack(
                            // clipBehavior: Clip.none,
                            // alignment: Alignment.center,
                            // fit: StackFit.expand,
                            fit: StackFit.passthrough,
                            children: [
                              Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: context.width,
                                ),
                              ),
                              Column(
                                children: [
                                  // Scrollable section starts here
                                  Expanded(
                                    child: SingleChildScrollView(
                                      controller: _scrollController,
                                      child: Stack(
                                        children: [
                                          // Positioned background images
                                          Positioned(
                                            right: -200,
                                            top: -200,
                                            child: Image.asset(
                                              'assets/images/png/top_right.png',
                                            ),
                                          ),
                                          Positioned(
                                            left: -500,
                                            top: 100,
                                            child: Image.asset(
                                              'assets/images/png/right.png',
                                            ),
                                          ),
                                          Positioned(
                                            top: context.height * 0.5,
                                            left: -720,
                                            bottom: context.height * 0.5,
                                            child: Image.asset(
                                              'assets/images/png/left.png',
                                            ),
                                          ),
                                          Positioned(
                                            top: context.height * 0.5,
                                            right: -720,
                                            bottom: context.height * 0.5,
                                            child: Image.asset(
                                              'assets/images/png/right.png',
                                            ),
                                          ),
                                          Positioned(
                                            left: -800,
                                            bottom: -500,
                                            child: Image.asset(
                                              'assets/images/png/bottom_left.png',
                                            ),
                                          ),
                                          Positioned(
                                            right: -800,
                                            bottom: -500,
                                            child: Image.asset(
                                              'assets/images/png/bottom_right.png',
                                            ),
                                          ),

                                          Column(
                                            key: _leaderBoardGlobalOBjectKey,
                                            children: [
                                              Padding(
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: context.width,
                                                ),
                                              ),
                                              // Non-scrollable top section: AppBar and NEXQLOUD INSIGHTS
                                              if (!isLiveViewOnly)
                                                AnimatedContainer(
                                                  duration: const Duration(
                                                    milliseconds: 300,
                                                  ),
                                                  color: _isMenuVisible
                                                      ? kBackgroundColor
                                                      : kTransparent,
                                                  child: FadeIn(
                                                    duration: const Duration(
                                                      milliseconds: 300,
                                                    ),
                                                    child: MainAppBar(
                                                      controller: _controller,
                                                      toggleMenu: _toggleMenu,
                                                    ),
                                                  ),
                                                ),
                                              Space.vertical(
                                                context.isDesktopExtraSmall
                                                    ? 20
                                                    : context.isDesktop
                                                        ? 40
                                                        : 10,
                                              ),
                                              FadeInUp(
                                                duration: const Duration(
                                                  milliseconds: 300,
                                                ),
                                                delay: const Duration(
                                                  milliseconds: 100,
                                                ),
                                                child: const WorldMap(),
                                              ),
                                              FadeInUp(
                                                duration: const Duration(
                                                  milliseconds: 300,
                                                ),
                                                delay: const Duration(
                                                  milliseconds: 200,
                                                ),
                                                child:
                                                    const DataAnalysisGauges(),
                                              ),
                                              if (!isLiveViewOnly)
                                                FadeInUp(
                                                  duration: const Duration(
                                                    milliseconds: 300,
                                                  ),
                                                  delay: const Duration(
                                                    milliseconds: 300,
                                                  ),
                                                  child: TransparentDataGrid(
                                                    onCellTapped: () {
                                                      Scrollable.ensureVisible(
                                                        _leaderBoardGlobalOBjectKey
                                                            .currentContext!,
                                                        duration:
                                                            const Duration(
                                                          milliseconds: 500,
                                                        ),
                                                        curve: Curves.easeInOut,
                                                      );
                                                    },
                                                  ),
                                                ),
                                              if (!isLiveViewOnly) ...[
                                                const LandingPageDescription(),
                                                const FooterArea(),
                                              ],
                                              const Space.vertical(34),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        ///Do-Not Remove This Code below
                        // Blurred header background when scrolling down (Desktop only)
                        // if (context.isDesktop)
                        //   AnimatedPositioned(
                        //     duration: const Duration(milliseconds: 350),
                        //     top: _isHeaderVisible ? 0 : -(kToolbarHeight + 100),
                        //     right: 0,
                        //     left: 0,
                        //     child: BlurredBackground(
                        //       borderRadius: 0,
                        //       blurAmount: 500,
                        //       child: AnimatedContainer(
                        //         duration: const Duration(milliseconds: 300),
                        //         decoration: BoxDecoration(
                        //           color: _isMenuVisible
                        //               ? kBackgroundColor
                        //               : kBackgroundColor.withOpacity(0.7),
                        //         ),
                        //         child: Center(
                        //           child: ConstrainedBox(
                        //             constraints: BoxConstraints(
                        //               maxWidth: context.horizontalPadding,
                        //             ),
                        //             child: Padding(
                        //               padding: EdgeInsets.zero,
                        //               child: MainAppBar(
                        //                 controller: _controller,
                        //                 toggleMenu: _toggleMenu,
                        //               ),
                        //             ),
                        //           ),
                        //         ),
                        //       ),
                        //     ),
                        //   ),
                      ],
                    ),
                  ),
          );
  }

  // Toggles the menu's visibility
  void _toggleMenu() {
    setState(() {
      _isMenuVisible = !_isMenuVisible;
      _isMenuVisible ? _controller.forward() : _controller.reverse();
    });
  }
}
