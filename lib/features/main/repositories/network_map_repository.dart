import 'package:nexqloud/core/services/api_service.dart';
import 'package:nexqloud/features/main/models/device_model.dart';
import 'package:nexqloud/features/main/models/server_info.dart';
import 'package:nexqloud/features/main/models/system_metrics.dart';
import 'package:nexqloud/features/main/models/table_data.dart';

class NetworkMapRepository {
  NetworkMapRepository({required ApiService apiService})
      : _apiService = apiService;

  final ApiService _apiService;

  String get url => 'network/map';

  Future<List<ServerInfo>> fetchServers() async {
    final endPoint = '$url/info';
    // 'Fetching Servers Data...'.printInfo();
    final response = await _apiService.get(
      endPoint,
      // {},
    );
    // 'Servers Data Fetched'.printInfo();
    // final serverList = (response as List<dynamic>)
    //     .whereType<Map<String, dynamic>>()
    //     .map((e) => ServerInfo.fromJson(e))
    //     .toList();

    final serverList = (response as List<dynamic>)
        .whereType<Map<String, dynamic>>()
        .map((e) => ServerInfo.fromJson(e))
        .where((server) =>
            server.latitude != null &&
            server.longitude != null &&
            (server.latitude != 0.0 || server.longitude != 0.0))
        .toList();

    return serverList;
  }

  Future<TableData> fetchTableDataFiltered({
    int page = 1,
    int limit = 10,
    bool? online,
    String? region,
    String? nftId,
  }) async {
    final endPoint = '$url/table';
    // 'Fetching Servers Data...'.printInfo();
    var body = {
      'page': page,
      'limit': limit,
    } as Map<String, dynamic>;
    if (online != null) {
      body = {
        ...body,
        'online': online,
      };
    }

    if (region != null) {
      body = {
        ...body,
        'region': region,
      };
    }

    if (nftId != null) {
      body = {
        ...body,
        "nft": nftId,
      };
    }

    final response = await _apiService.post(
      endPoint,
      body,
    );
    // 'Servers Data Fetched'.printInfo();
    return TableData.fromJson(response);
  }

  Future<SystemMetrics> fetchSystemMetrics() async {
    final endPoint = '$url/device/aggregates';
    // 'Fetching System Metrics...'.printInfo();
    final response = await _apiService.get(
      endPoint,
    );
    // response.printInfo(tag: 'System Metrics');
    // 'System Metrics Fetched'.printInfo();
    return SystemMetrics.fromJson(response);
  }

  Future<DeviceModel> fetchSingleServerData(String hostName) async {
    final endPoint = '$url/device/data';
    // 'Fetching Single Server Data...'.printInfo();
    final response = await _apiService.post(
      endPoint,
      {
        'hostName': hostName,
      },
    );
    // 'Fetched Single Server Data'.printInfo();
    return DeviceModel.fromJson(response);
  }

  Future<DeviceModel> fetchSingleServerDataViaServerId(String serverId) async {
    final endPoint = '$url/device/data/v2';
    // 'Fetching Single Server Data...'.printInfo();
    final response = await _apiService.post(
      endPoint,
      {
        'serverId': serverId,
      },
    );
    // print('Testing: ################ $response');
    // 'Fetched Single Server Data'.printInfo();
    return DeviceModel.fromJson(response);
  }

  Future<List<String>> fetchRegionsData() async {
    const endPoint = 'https://devjobs.dks.nexqloud.net/jobs/device/regions';
    // 'Fetching Regions Data...'.printInfo();
    final response = await _apiService.post(
      endPoint,
      {},
      useDefaultBaseUrl: false,
    );
    // 'Fetched Regions Data'.printInfo();
    if (response.isNotEmpty) {
      return [];
    } else {
      return [];
    }
  }
}
