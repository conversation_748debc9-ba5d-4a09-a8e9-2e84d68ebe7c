///This file is automatically generated. DO NOT EDIT, all your changes would be lost.
class Assets {
  Assets._();

  static const String assetsAfrica = 'assets/africa.json';
  static const String assetsAsia = 'assets/asia.json';
  static const String assetsAustralia = 'assets/australia.json';
  static const String assetsEurope = 'assets/europe.json';
  static const String assetsNorthAmerica = 'assets/north-america.json';
  static const String assetsOceania = 'assets/oceania.json';
  static const String assetsServersList = 'assets/servers_list.xlsx';
  static const String assetsSouthAmerica = 'assets/south-america.json';
  static const String assetsWorldMap = 'assets/world_map.json';
  static const String lottieSelectedMarkerLottie =
      'assets/lottie/selected_marker_lottie.json';
  static const String networkMapAssetsServersList = 'assets/servers_list.xlsx';
  static const String networkMapAssetsWorldMap = 'assets/world_map.json';
  static const String pngAbg = 'assets/images/png/abg.png';
  static const String pngAppBarBg = 'assets/images/png/app_bar_bg.png';
  static const String pngBottomLeft = 'assets/images/png/bottom_left.png';
  static const String pngBottomRight = 'assets/images/png/bottom_right.png';
  static const String pngCpu = 'assets/images/png/cpu.png';
  static const String pngInfoModalBg = 'assets/images/png/info_modal_bg.png';
  static const String pngInfoModalBgUpdated =
      'assets/images/png/info_modal_bg_updated.png';
  static const String pngInfoModalBgUpdatedSmall =
      'assets/images/png/info_modal_bg_updated_small.png';
  static const String pngLeft = 'assets/images/png/left.png';
  static const String pngLicenseImg = 'assets/images/png/license_img.png';
  static const String pngMiddle = 'assets/images/png/middle.png';
  static const String pngOnlineIcon = 'assets/icons/png/online_icon.png';
  static const String pngOnlineStatusIcon =
      'assets/icons/png/online_status_icon.png';
  static const String pngPopupModalGlow =
      'assets/images/png/popup_modal_glow.png';
  static const String pngRight = 'assets/images/png/right.png';
  static const String pngSecureIcon = 'assets/icons/png/secure_icon.png';
  static const String pngServerChipImage =
      'assets/images/png/server_chip_image.png';
  static const String pngServerImage = 'assets/images/png/server_image.png';
  static const String pngServerInfoOne =
      'assets/images/png/server_info_one.png';
  static const String pngServerModal = 'assets/images/png/server_modal.png';
  static const String pngServerModalBg =
      'assets/images/png/server_modal_bg.png';
  static const String pngServerModalBgNoShadow =
      'assets/images/png/server_modal_bg_no_shadow.png';
  static const String pngServerModalHd =
      'assets/images/png/server_modal_hd.png';
  static const String pngTopCardUi = 'assets/images/png/top_card_ui.png';
  static const String pngTopRight = 'assets/images/png/top_right.png';
  static const String pngWarningIcon = 'assets/icons/png/warning_icon.png';
  static const String rubikRubikBlack = 'assets/fonts/rubik/Rubik-Black.ttf';
  static const String rubikRubikBlackItalic =
      'assets/fonts/rubik/Rubik-BlackItalic.ttf';
  static const String rubikRubikBold = 'assets/fonts/rubik/Rubik-Bold.ttf';
  static const String rubikRubikBoldItalic =
      'assets/fonts/rubik/Rubik-BoldItalic.ttf';
  static const String rubikRubikExtraBold =
      'assets/fonts/rubik/Rubik-ExtraBold.ttf';
  static const String rubikRubikExtraBoldItalic =
      'assets/fonts/rubik/Rubik-ExtraBoldItalic.ttf';
  static const String rubikRubikItalic = 'assets/fonts/rubik/Rubik-Italic.ttf';
  static const String rubikRubikLight = 'assets/fonts/rubik/Rubik-Light.ttf';
  static const String rubikRubikLightItalic =
      'assets/fonts/rubik/Rubik-LightItalic.ttf';
  static const String rubikRubikMedium = 'assets/fonts/rubik/Rubik-Medium.ttf';
  static const String rubikRubikMediumItalic =
      'assets/fonts/rubik/Rubik-MediumItalic.ttf';
  static const String rubikRubikRegular =
      'assets/fonts/rubik/Rubik-Regular.ttf';
  static const String rubikRubikSemiBold =
      'assets/fonts/rubik/Rubik-SemiBold.ttf';
  static const String rubikRubikSemiBoldItalic =
      'assets/fonts/rubik/Rubik-SemiBoldItalic.ttf';
  static const String svgAppleIconWhite =
      'assets/icons/svg/apple_icon_white.svg';
  static const String svgCpuMeterIcon = 'assets/icons/svg/cpu_meter_icon.svg';
  static const String svgDropDownArrowDown =
      'assets/icons/svg/drop_down_arrow_down.svg';
  static const String svgFacebookCircularIcon =
      'assets/icons/svg/facebook_circular_icon.svg';
  static const String svgFacebookIcon = 'assets/icons/svg/facebook_icon.svg';
  static const String svgGooglePlayIconWhite =
      'assets/icons/svg/google_play_icon_white.svg';
  static const String svgGpuIcon = 'assets/icons/svg/gpu_icon.svg';
  static const String svgGreenCircleTickIcon =
      'assets/icons/svg/green_circle_tick_icon.svg';
  static const String svgLicenseKeyIcon =
      'assets/icons/svg/license_key_icon.svg';
  static const String svgLinkedCircularIcon =
      'assets/icons/svg/linked_circular_icon.svg';
  static const String svgLinkedInIcon = 'assets/icons/svg/linked_in_icon.svg';
  static const String svgLocationPinIcon =
      'assets/icons/svg/location_pin_icon.svg';
  static const String svgPinLocationWhite =
      'assets/icons/svg/pin_location_white.svg';
  static const String svgMinusIcon = 'assets/icons/svg/minus_icon.svg';
  static const String svgMoneyIcon = 'assets/icons/svg/money_icon.svg';
  static const String svgNexLogo = 'assets/icons/svg/nex_logo.svg';
  static const String svgPlusIcon = 'assets/icons/svg/plus_icon.svg';
  static const String svgQloudScoreCloudIcon =
      'assets/icons/svg/qloud_score_cloud_icon.svg';
  static const String svgRefreshIcon = 'assets/icons/svg/refresh_icon.svg';
  static const String svgResetMap = 'assets/icons/svg/reset_map.svg';
  static const String svgSearchIcon = 'assets/icons/svg/search_icon.svg';
  static const String svgServer = 'assets/icons/svg/server.svg';
  static const String svgServerIconWhite =
      'assets/icons/svg/server_icon_white.svg';
  static const String svgUptimeClockIcon =
      'assets/icons/svg/uptime_clock_icon.svg';
  static const String svgXCircularIcon = 'assets/icons/svg/x_circular_icon.svg';
  static const String svgXIcon = 'assets/icons/svg/x_icon.svg';
  static const String svgZoomIn = 'assets/icons/svg/zoom_in.svg';
  static const String svgZoomOut = 'assets/icons/svg/zoom_out.svg';
  static const String svgExpandMap = 'assets/icons/svg/mi_expand.svg';
  static const String svgCollapseMap = 'assets/icons/svg/map_collapse_icon.svg';
  static const String pngSImage = 'assets/images/png/s_image.png';
  static const String tooltipBubble = 'assets/images/svg/tooltip_bubble.svg';
}
