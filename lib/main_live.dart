import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:nexqloud/core/app/app.dart';
import 'package:nexqloud/core/constants/env_constants.dart';
import 'package:nexqloud/core/services/error_manager.dart';

void main() {
  const appVersion = String.fromEnvironment('APP_VERSION') ?? 'Unknown';
  const baseUriMain = String.fromEnvironment(
    'API_URL',
  );
  // Log to the browser console
  print('App Version: v$appVersion');
  print('Api URL: $baseUriMain/');

  EnvConstants.appEnv = Env.LIVE;

  if (!kDebugMode) {
    _setupErrorManagement();
  }

  // NetworkServices.fetchServersTableData();
  runApp(const App());
}

void _setupErrorManagement() async {
  await ErrorManager.init();
  FlutterError.onError = (details) {
    ErrorManager.reportError(
      details.exception,
      details.stack,
    );
  };
}
// flutter run -d web-server
